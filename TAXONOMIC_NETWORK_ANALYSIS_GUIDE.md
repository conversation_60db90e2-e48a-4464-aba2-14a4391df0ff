# Taxonomic-Level Network Analysis Guide

## Overview
This document describes the new taxonomic-level network analysis approach that solves the computational and biological bias issues of the original OTU-level analysis.

## Problem with Original Approach
- **Memory Issues**: 79,887 bacteria OTUs required >20GB RAM for correlation matrices
- **Abundance Bias**: Filtering to top 3,000 most abundant OTUs introduced bias
- **Biological Interpretation**: Individual OTUs are difficult to interpret ecologically

## Solution: Family-Level Analysis
Aggregate OTUs to family taxonomic level before network analysis.

### Benefits:
1. **Computational Efficiency**: 199 families vs 79,887 OTUs
2. **No Abundance Bias**: All families represented proportionally
3. **Biological Relevance**: Family-level interactions are ecologically meaningful
4. **Memory Efficient**: <1MB vs >20GB memory requirement

## Taxonomic Aggregation Results

### Bacteria (Alpine Dataset):
- **Original**: 79,887 OTUs
- **Family Level**: 199 families (58,185 OTUs with missing family assignments removed)
- **After filtering**: 165 variable families per environment

### Fungi (Alpine Dataset):
- **Original**: 6,269 OTUs  
- **Family Level**: 295 families (3,064 OTUs with missing family assignments removed)
- **After filtering**: ~250 variable families per environment

## Network Analysis Structure

### 8 Individual Networks (Family Level):
1. **Bacteria Alpine Old**: ~165 families
2. **Bacteria Alpine New**: ~165 families
3. **Bacteria Farming Old**: ~140 families
4. **Bacteria Farming New**: ~140 families
5. **Fungi Alpine Old**: ~250 families
6. **Fungi Alpine New**: ~250 families
7. **Fungi Farming Old**: ~220 families
8. **Fungi Farming New**: ~220 families

### 4 Cross-Correlation Networks:
1. **Bacteria vs Fungi (Alpine Old)**: ~165 × ~250 families
2. **Bacteria vs Fungi (Alpine New)**: ~165 × ~250 families
3. **Bacteria vs Fungi (Farming Old)**: ~140 × ~220 families
4. **Bacteria vs Fungi (Farming New)**: ~140 × ~220 families

## Script Usage

### Option 1: Family Level (Recommended)
```bash
# Edit network_taxonomic_level.R to set:
TAXONOMIC_LEVEL <- "Family"

# Run analysis:
& "C:\Program Files\R\R-4.4.2\bin\x64\R.exe" --slave -f network_taxonomic_level.R
```

### Option 2: Genus Level (More Detailed)
```bash
# Edit network_taxonomic_level.R to set:
TAXONOMIC_LEVEL <- "Genus"

# Run analysis:
& "C:\Program Files\R\R-4.4.2\bin\x64\R.exe" --slave -f network_taxonomic_level.R
```

### Option 3: Order Level (Broader Groups)
```bash
# Edit network_taxonomic_level.R to set:
TAXONOMIC_LEVEL <- "Order"

# Run analysis:
& "C:\Program Files\R\R-4.4.2\bin\x64\R.exe" --slave -f network_taxonomic_level.R
```

## Expected Runtime and Resources

### Family Level Analysis:
- **Runtime**: 5-15 minutes
- **Memory**: <2GB RAM
- **Output**: 12 network files (8 individual + 4 cross-correlations)

### Genus Level Analysis:
- **Runtime**: 15-30 minutes  
- **Memory**: <4GB RAM
- **Output**: 12 network files with higher resolution

## Comparison: OTU vs Family Level

| Aspect | OTU Level (Original) | Family Level (New) |
|--------|---------------------|-------------------|
| **Bacteria Taxa** | 79,887 OTUs | 199 families |
| **Fungi Taxa** | 6,269 OTUs | 295 families |
| **Memory Required** | >20GB | <1GB |
| **Processing Time** | Hours (if successful) | Minutes |
| **Bias** | Abundance-biased | Proportionally representative |
| **Interpretation** | Difficult | Ecologically meaningful |
| **Network Size** | Unmanageable | Interpretable |

## Biological Interpretation Advantages

### Family-Level Networks Show:
- **Functional Groups**: Families often share similar ecological roles
- **Taxonomic Interactions**: Meaningful inter-family relationships
- **Hub Families**: Key taxonomic groups in the ecosystem
- **Community Structure**: Family-level community organization

### Examples of Interpretable Results:
- **Rhizobiaceae** (nitrogen-fixing bacteria) correlations with plant-associated fungi
- **Acidobacteriaceae** (soil bacteria) interactions with decomposer fungi
- **Mycorrhizal fungi families** correlations with root-associated bacteria

## Recommendations

1. **Primary Analysis**: Use Family level for main results
2. **Detailed Analysis**: Use Genus level for specific questions
3. **Broad Patterns**: Use Order level for overview
4. **Validation**: Compare results across taxonomic levels

## Files Generated

### Individual Networks:
- `Bacteria_Alpine_Old_Family_network.pdf`
- `Bacteria_Alpine_New_Family_network.pdf`
- `Bacteria_Farming_Old_Family_network.pdf`
- `Bacteria_Farming_New_Family_network.pdf`
- `Fungi_Alpine_Old_Family_network.pdf`
- `Fungi_Alpine_New_Family_network.pdf`
- `Fungi_Farming_Old_Family_network.pdf`
- `Fungi_Farming_New_Family_network.pdf`

### Cross-Correlation Networks:
- `Bacteria_Fungi_Alpine_Old_Family_cross_network.pdf`
- `Bacteria_Fungi_Alpine_New_Family_cross_network.pdf`
- `Bacteria_Fungi_Farming_Old_Family_cross_network.pdf`
- `Bacteria_Fungi_Farming_New_Family_cross_network.pdf`

### Data Files:
- Hub families CSV files
- Network metrics comparison tables
- Community detection results

This approach provides scientifically sound, computationally efficient, and biologically interpretable network analysis results.
