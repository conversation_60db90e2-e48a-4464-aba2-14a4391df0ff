# Test a portion of the taxonomic network script
library(readxl)
library(igraph)
library(vegan)
library(tidyverse)

# Configuration
TAXONOMIC_LEVEL <- "Family"
cat("Testing network analysis at", TAXONOMIC_LEVEL, "level\n")

# Read one dataset for testing
bacteria_alpine_otu <- read_excel("OTU_table_bacteria_alpine_samples.xlsx")
bacteria_alpine_tax <- read_excel("taxonomy_bacteria_alpine_samples.xlsx")
bacteria_alpine_meta <- read_excel("metadata_bacteria_alpine_samples.xlsx")

cat("Original bacteria alpine OTU dimensions:", dim(bacteria_alpine_otu), "\n")

# Function to aggregate OTUs to specified taxonomic level
aggregate_to_taxonomic_level <- function(otu_table, taxonomy_table, taxonomic_level) {
  cat("\n--- Aggregating to", taxonomic_level, "level ---\n")
  
  # Check if taxonomic level exists in taxonomy table
  if (!taxonomic_level %in% colnames(taxonomy_table)) {
    stop("Taxonomic level '", taxonomic_level, "' not found in taxonomy table")
  }
  
  # Get the first column name (OTU ID column)
  otu_id_col <- colnames(otu_table)[1]
  tax_otu_id_col <- colnames(taxonomy_table)[1]
  
  # Merge OTU table with taxonomy
  merged_data <- merge(otu_table, taxonomy_table[, c(tax_otu_id_col, taxonomic_level)], 
                       by.x = otu_id_col, by.y = tax_otu_id_col, all.x = TRUE)
  
  # Remove rows with NA or empty taxonomic assignment
  valid_rows <- !is.na(merged_data[[taxonomic_level]]) & 
                merged_data[[taxonomic_level]] != "" & 
                merged_data[[taxonomic_level]] != "NA"
  
  if (sum(valid_rows) == 0) {
    stop("No valid taxonomic assignments found at ", taxonomic_level, " level")
  }
  
  cat("Removed", sum(!valid_rows), "OTUs with missing", taxonomic_level, "assignment\n")
  merged_data <- merged_data[valid_rows, ]
  
  # Get sample columns (all columns except OTU ID and taxonomic level)
  sample_cols <- setdiff(colnames(merged_data), c(otu_id_col, taxonomic_level))
  
  # Aggregate by summing OTU counts within each taxonomic group
  aggregated <- merged_data %>%
    group_by(!!sym(taxonomic_level)) %>%
    summarise(across(all_of(sample_cols), \(x) sum(x, na.rm = TRUE)), .groups = 'drop')
  
  # Rename the taxonomic level column to match original OTU ID column name
  colnames(aggregated)[1] <- otu_id_col
  
  cat("Aggregated from", nrow(otu_table), "OTUs to", nrow(aggregated), taxonomic_level, "groups\n")
  
  return(as.data.frame(aggregated))
}

# Function to filter samples by environment
filter_samples_by_environment <- function(otu_table, metadata, environment_type) {
  sample_id_col <- if ("Sample_ID" %in% colnames(metadata)) "Sample_ID" else "sample"
  target_samples <- metadata[[sample_id_col]][grepl(environment_type, metadata$Environment, ignore.case = TRUE)]
  cat("Found", length(target_samples), "target samples for environment", environment_type, "\n")
  
  sample_cols <- which(colnames(otu_table) %in% target_samples)
  if (length(sample_cols) == 0) return(NULL)
  
  filtered_table <- otu_table[, c(1, sample_cols)]
  cat("Filtered", environment_type, "samples:", ncol(filtered_table) - 1, "samples\n")
  return(filtered_table)
}

# Function to create correlation matrix for taxonomic-level data
create_taxonomic_correlation_matrix <- function(taxonomic_table, threshold = 0.6) {
  cat("\n--- Creating correlation matrix ---\n")
  cat("Input dimensions:", dim(taxonomic_table), "\n")

  if (ncol(taxonomic_table) <= 1) {
    stop("Table must have at least one sample column besides the taxonomic IDs")
  }

  tryCatch({
    # Extract abundance matrix (remove first column which contains taxonomic names)
    abundance_matrix <- as.matrix(taxonomic_table[,-1])
    rownames(abundance_matrix) <- taxonomic_table[[1]]

    # Check for rows with all zeros or constant values
    row_sums <- rowSums(abundance_matrix)
    constant_rows <- row_sums == 0 | apply(abundance_matrix, 1, function(x) length(unique(x)) <= 1)
    
    if (any(constant_rows)) {
      cat("Removing", sum(constant_rows), "constant rows\n")
      abundance_matrix <- abundance_matrix[!constant_rows, , drop = FALSE]
    }

    if (nrow(abundance_matrix) <= 1) {
      stop("Not enough variable rows for correlation analysis")
    }

    cat("Final matrix for correlation:", nrow(abundance_matrix), "taxa ×", ncol(abundance_matrix), "samples\n")

    # Calculate correlations
    cat("Calculating Spearman correlations...\n")
    cor_matrix <- cor(t(abundance_matrix), method = "spearman", use = "pairwise.complete.obs")

    # Replace any NAs with 0
    if (any(is.na(cor_matrix))) {
      cat("Replacing", sum(is.na(cor_matrix)), "NA values with 0\n")
      cor_matrix[is.na(cor_matrix)] <- 0
    }

    # Apply threshold
    cor_matrix[abs(cor_matrix) < threshold] <- 0
    diag(cor_matrix) <- 0

    cat("After thresholding: ", sum(cor_matrix != 0), " non-zero correlations\n")

    return(list(
      cor_matrix = cor_matrix,
      taxa_names = rownames(abundance_matrix),
      n_taxa = nrow(abundance_matrix)
    ))
    
  }, error = function(e) {
    cat("Error in correlation calculation:", e$message, "\n")
    return(NULL)
  })
}

# Test the workflow
cat("\n========== TESTING TAXONOMIC WORKFLOW ==========\n")

# 1. Aggregate to family level
bacteria_alpine_agg <- aggregate_to_taxonomic_level(bacteria_alpine_otu, bacteria_alpine_tax, TAXONOMIC_LEVEL)

# 2. Filter to old samples
bacteria_alpine_old <- filter_samples_by_environment(bacteria_alpine_agg, bacteria_alpine_meta, "old")

# 3. Create correlation matrix
if (!is.null(bacteria_alpine_old)) {
  bacteria_alpine_old_cor <- create_taxonomic_correlation_matrix(bacteria_alpine_old, threshold = 0.6)
  
  if (!is.null(bacteria_alpine_old_cor)) {
    cat("\nSUCCESS: Correlation matrix created!\n")
    cat("Matrix dimensions:", dim(bacteria_alpine_old_cor$cor_matrix), "\n")
    cat("Number of taxa:", bacteria_alpine_old_cor$n_taxa, "\n")
    cat("Non-zero correlations:", sum(bacteria_alpine_old_cor$cor_matrix != 0), "\n")
  } else {
    cat("FAILED: Could not create correlation matrix\n")
  }
} else {
  cat("FAILED: Could not filter samples\n")
}

cat("\nTaxonomic network test completed!\n")
