# Network Analysis at Taxonomic Level (Genus or Family)

# Load required libraries
library(readxl)
library(igraph)
library(vegan)
library(tidyverse)

# Configuration: Choose taxonomic level for analysis
TAXONOMIC_LEVEL <- "Family"  # Options: "Genus", "Family", "Order", "Class"
cat("Performing network analysis at", TAXONOMIC_LEVEL, "level\n")
cat("This provides optimal balance between computational efficiency and biological resolution\n")

# Read data files for the new experiment with four datasets
cat("========== LOADING DATA FILES ==========\n")

# Bacteria datasets
bacteria_alpine_otu <- read_excel("OTU_table_bacteria_alpine_samples.xlsx")
bacteria_farming_otu <- read_excel("OTU_table_bacteria_farming_samples.xlsx")

# Fungi datasets  
fungi_alpine_otu <- read_excel("OTU_table_fungi_alpine_samples.xlsx")
fungi_farming_otu <- read_excel("OTU_table_fungi_farming_samples.xlsx")

# Taxonomy files
bacteria_alpine_tax <- read_excel("taxonomy_bacteria_alpine_samples.xlsx")
bacteria_farming_tax <- read_excel("taxonomy_bacteria_farming_samples.xlsx")
fungi_alpine_tax <- read_excel("taxonomy_fungi_alpine_samples.xlsx")
fungi_farming_tax <- read_excel("taxonomy_fungi_farming_samples.xlsx")

# Metadata files
bacteria_alpine_meta <- read_excel("metadata_bacteria_alpine_samples.xlsx")
bacteria_farming_meta <- read_excel("metadata_bacteria_farming_samples.xlsx")
fungi_alpine_meta <- read_excel("metadata_fungi_alpine_samples.xlsx")
fungi_farming_meta <- read_excel("metadata_fungi_farming_samples.xlsx")

# Print data dimensions for debugging
cat("Original data dimensions:\n")
cat("Bacteria Alpine OTU:", dim(bacteria_alpine_otu), "\n")
cat("Bacteria Farming OTU:", dim(bacteria_farming_otu), "\n")
cat("Fungi Alpine OTU:", dim(fungi_alpine_otu), "\n")
cat("Fungi Farming OTU:", dim(fungi_farming_otu), "\n")

# Function to aggregate OTUs to specified taxonomic level
aggregate_to_taxonomic_level <- function(otu_table, taxonomy_table, taxonomic_level) {
  cat("\n--- Aggregating to", taxonomic_level, "level ---\n")
  
  # Check if taxonomic level exists in taxonomy table
  if (!taxonomic_level %in% colnames(taxonomy_table)) {
    stop("Taxonomic level '", taxonomic_level, "' not found in taxonomy table")
  }
  
  # Get the first column name (OTU ID column)
  otu_id_col <- colnames(otu_table)[1]
  tax_otu_id_col <- colnames(taxonomy_table)[1]
  
  # Merge OTU table with taxonomy
  merged_data <- merge(otu_table, taxonomy_table[, c(tax_otu_id_col, taxonomic_level)], 
                       by.x = otu_id_col, by.y = tax_otu_id_col, all.x = TRUE)
  
  # Remove rows with NA or empty taxonomic assignment
  valid_rows <- !is.na(merged_data[[taxonomic_level]]) & 
                merged_data[[taxonomic_level]] != "" & 
                merged_data[[taxonomic_level]] != "NA"
  
  if (sum(valid_rows) == 0) {
    stop("No valid taxonomic assignments found at ", taxonomic_level, " level")
  }
  
  cat("Removed", sum(!valid_rows), "OTUs with missing", taxonomic_level, "assignment\n")
  merged_data <- merged_data[valid_rows, ]
  
  # Get sample columns (all columns except OTU ID and taxonomic level)
  sample_cols <- setdiff(colnames(merged_data), c(otu_id_col, taxonomic_level))
  
  # Aggregate by summing OTU counts within each taxonomic group
  aggregated <- merged_data %>%
    group_by(!!sym(taxonomic_level)) %>%
    summarise(across(all_of(sample_cols), sum, na.rm = TRUE), .groups = 'drop')
  
  # Rename the taxonomic level column to match original OTU ID column name
  colnames(aggregated)[1] <- otu_id_col
  
  cat("Aggregated from", nrow(otu_table), "OTUs to", nrow(aggregated), taxonomic_level, "groups\n")
  
  return(as.data.frame(aggregated))
}

# Function to filter samples by environment (old vs new)
filter_samples_by_environment <- function(otu_table, metadata, environment_type) {
  # Determine the sample ID column name (different files use different names)
  sample_id_col <- NULL
  if ("Sample_ID" %in% colnames(metadata)) {
    sample_id_col <- "Sample_ID"
  } else if ("sample" %in% colnames(metadata)) {
    sample_id_col <- "sample"
  } else {
    cat("Error: Could not find sample ID column in metadata\n")
    return(NULL)
  }
  
  # Get sample IDs for the specified environment
  target_samples <- metadata[[sample_id_col]][grepl(environment_type, metadata$Environment, ignore.case = TRUE)]
  
  cat("Found", length(target_samples), "target samples for environment", environment_type, "\n")
  
  # Find which columns in OTU table correspond to these samples
  sample_cols <- which(colnames(otu_table) %in% target_samples)
  
  if (length(sample_cols) == 0) {
    cat("Warning: No samples found for environment", environment_type, "\n")
    return(NULL)
  }
  
  # Return OTU table with only the target samples (plus the first column with IDs)
  filtered_table <- otu_table[, c(1, sample_cols)]
  cat("Filtered", environment_type, "samples:", ncol(filtered_table) - 1, "samples\n")
  return(filtered_table)
}

# Aggregate all datasets to the specified taxonomic level
cat("\n========== AGGREGATING TO TAXONOMIC LEVEL ==========\n")

bacteria_alpine_agg <- aggregate_to_taxonomic_level(bacteria_alpine_otu, bacteria_alpine_tax, TAXONOMIC_LEVEL)
bacteria_farming_agg <- aggregate_to_taxonomic_level(bacteria_farming_otu, bacteria_farming_tax, TAXONOMIC_LEVEL)
fungi_alpine_agg <- aggregate_to_taxonomic_level(fungi_alpine_otu, fungi_alpine_tax, TAXONOMIC_LEVEL)
fungi_farming_agg <- aggregate_to_taxonomic_level(fungi_farming_otu, fungi_farming_tax, TAXONOMIC_LEVEL)

cat("\nAggregated data dimensions:\n")
cat("Bacteria Alpine:", dim(bacteria_alpine_agg), "\n")
cat("Bacteria Farming:", dim(bacteria_farming_agg), "\n")
cat("Fungi Alpine:", dim(fungi_alpine_agg), "\n")
cat("Fungi Farming:", dim(fungi_farming_agg), "\n")

# Create filtered datasets for old and new samples
cat("\n========== FILTERING SAMPLES BY ENVIRONMENT ==========\n")

# Bacteria Alpine
bacteria_alpine_old <- filter_samples_by_environment(bacteria_alpine_agg, bacteria_alpine_meta, "old")
bacteria_alpine_new <- filter_samples_by_environment(bacteria_alpine_agg, bacteria_alpine_meta, "new")

# Bacteria Farming  
bacteria_farming_old <- filter_samples_by_environment(bacteria_farming_agg, bacteria_farming_meta, "old")
bacteria_farming_new <- filter_samples_by_environment(bacteria_farming_agg, bacteria_farming_meta, "new")

# Fungi Alpine
fungi_alpine_old <- filter_samples_by_environment(fungi_alpine_agg, fungi_alpine_meta, "old")
fungi_alpine_new <- filter_samples_by_environment(fungi_alpine_agg, fungi_alpine_meta, "new")

# Fungi Farming
fungi_farming_old <- filter_samples_by_environment(fungi_farming_agg, fungi_farming_meta, "old")
fungi_farming_new <- filter_samples_by_environment(fungi_farming_agg, fungi_farming_meta, "new")

cat("\n========== SUMMARY OF FILTERED DATASETS ==========\n")
if (!is.null(bacteria_alpine_old)) cat("Bacteria Alpine Old:", dim(bacteria_alpine_old), "\n")
if (!is.null(bacteria_alpine_new)) cat("Bacteria Alpine New:", dim(bacteria_alpine_new), "\n")
if (!is.null(bacteria_farming_old)) cat("Bacteria Farming Old:", dim(bacteria_farming_old), "\n")
if (!is.null(bacteria_farming_new)) cat("Bacteria Farming New:", dim(bacteria_farming_new), "\n")
if (!is.null(fungi_alpine_old)) cat("Fungi Alpine Old:", dim(fungi_alpine_old), "\n")
if (!is.null(fungi_alpine_new)) cat("Fungi Alpine New:", dim(fungi_alpine_new), "\n")
if (!is.null(fungi_farming_old)) cat("Fungi Farming Old:", dim(fungi_farming_old), "\n")
if (!is.null(fungi_farming_new)) cat("Fungi Farming New:", dim(fungi_farming_new), "\n")

cat("\nData aggregation and filtering completed successfully!\n")

# Function to create correlation matrix for taxonomic-level data
create_taxonomic_correlation_matrix <- function(taxonomic_table, threshold = 0.6) {
  cat("\n--- Creating correlation matrix ---\n")
  cat("Input dimensions:", dim(taxonomic_table), "\n")

  # Check if the table has at least one column besides the taxonomic IDs
  if (ncol(taxonomic_table) <= 1) {
    stop("Table must have at least one sample column besides the taxonomic IDs")
  }

  tryCatch({
    # Extract abundance matrix (remove first column which contains taxonomic names)
    abundance_matrix <- as.matrix(taxonomic_table[,-1])
    rownames(abundance_matrix) <- taxonomic_table[[1]]

    # Check for rows with all zeros or constant values
    row_sums <- rowSums(abundance_matrix)
    constant_rows <- row_sums == 0 | apply(abundance_matrix, 1, function(x) length(unique(x)) <= 1)

    if (any(constant_rows)) {
      cat("Removing", sum(constant_rows), "constant rows (all zeros or all same value)\n")
      abundance_matrix <- abundance_matrix[!constant_rows, , drop = FALSE]
    }

    # Check if we still have enough rows
    if (nrow(abundance_matrix) <= 1) {
      stop("Not enough variable rows for correlation analysis after removing constant rows")
    }

    cat("Final matrix for correlation:", nrow(abundance_matrix), "taxa ×", ncol(abundance_matrix), "samples\n")

    # Calculate correlations
    cat("Calculating Spearman correlations...\n")
    cor_matrix <- cor(t(abundance_matrix), method = "spearman", use = "pairwise.complete.obs")

    # Replace any NAs with 0
    if (any(is.na(cor_matrix))) {
      cat("Replacing", sum(is.na(cor_matrix)), "NA values with 0\n")
      cor_matrix[is.na(cor_matrix)] <- 0
    }

    # Print summary before thresholding
    cat("\nBefore thresholding:\n")
    cat("Total correlations:", length(cor_matrix), "\n")
    cat("Positive correlations:", sum(cor_matrix > 0), "\n")
    cat("Negative correlations:", sum(cor_matrix < 0), "\n")
    cat("Range:", range(cor_matrix), "\n")

    # Store signs before thresholding
    signs <- sign(cor_matrix)

    # Apply threshold to absolute values
    cor_matrix[abs(cor_matrix) < threshold] <- 0

    # Restore signs
    cor_matrix <- cor_matrix * signs

    # Set diagonal to zero
    diag(cor_matrix) <- 0

    # Print summary after thresholding
    cat("\nAfter thresholding (|correlation| ≥", threshold, "):\n")
    cat("Total non-zero correlations:", sum(cor_matrix != 0), "\n")
    cat("Positive correlations:", sum(cor_matrix > 0), "\n")
    cat("Negative correlations:", sum(cor_matrix < 0), "\n")

    if (sum(cor_matrix != 0) > 0) {
      cat("Range of non-zero correlations:", range(cor_matrix[cor_matrix != 0]), "\n")
    } else {
      cat("No correlations above threshold.\n")
    }

    return(list(
      cor_matrix = cor_matrix,
      taxa_names = rownames(abundance_matrix),
      n_taxa = nrow(abundance_matrix)
    ))

  }, error = function(e) {
    cat("Error in correlation calculation:", e$message, "\n")
    return(NULL)
  })
}

# Function to analyze taxonomic network
analyze_taxonomic_network <- function(cor_result, group_name) {
  if (is.null(cor_result)) {
    cat("Cannot analyze network: correlation result is NULL\n")
    return(NULL)
  }

  cat("\n========== ANALYZING", group_name, "NETWORK ==========\n")

  cor_matrix <- cor_result$cor_matrix
  taxa_names <- cor_result$taxa_names

  # Check if we have any correlations
  if (sum(cor_matrix != 0) == 0) {
    cat("No correlations above threshold. Cannot create network.\n")
    return(NULL)
  }

  tryCatch({
    # Create igraph network from correlation matrix
    network <- graph_from_adjacency_matrix(
      abs(cor_matrix),
      mode = "undirected",
      weighted = TRUE,
      diag = FALSE
    )

    # Add taxa names as vertex names
    V(network)$name <- taxa_names

    # Add edge signs (positive/negative correlations)
    edge_signs <- ifelse(cor_matrix[cor_matrix != 0] > 0, "positive", "negative")
    E(network)$sign <- edge_signs
    E(network)$correlation <- cor_matrix[cor_matrix != 0]

    # Calculate network metrics
    cat("Network created with", vcount(network), "nodes and", ecount(network), "edges\n")

    # Node degree
    node_degrees <- degree(network)
    cat("Average degree:", mean(node_degrees), "\n")
    cat("Max degree:", max(node_degrees), "\n")

    # Centrality measures
    betweenness <- betweenness(network)
    closeness <- closeness(network)

    # Community detection
    communities <- cluster_louvain(network)
    community_count <- length(communities)
    modularity_score <- modularity(communities)

    cat("Communities detected:", community_count, "\n")
    cat("Modularity:", modularity_score, "\n")

    # Identify hub nodes (top 10% by degree)
    degree_threshold <- quantile(node_degrees, 0.9)
    hub_nodes <- names(node_degrees[node_degrees >= degree_threshold])
    cat("Hub nodes (top 10% by degree):", length(hub_nodes), "\n")

    # Create results list
    results <- list(
      network = network,
      node_degrees = node_degrees,
      betweenness = betweenness,
      closeness = closeness,
      communities = communities,
      community_count = community_count,
      modularity = modularity_score,
      hub_nodes = hub_nodes,
      taxa_names = taxa_names,
      group_name = group_name
    )

    return(results)

  }, error = function(e) {
    cat("Error in network analysis:", e$message, "\n")
    return(NULL)
  })
}

# Perform individual network analyses for each dataset
cat("\n\n========== INDIVIDUAL NETWORK ANALYSES ==========\n")

# Bacteria Alpine Old
if (!is.null(bacteria_alpine_old)) {
  bacteria_alpine_old_cor <- create_taxonomic_correlation_matrix(bacteria_alpine_old, threshold = 0.6)
  bacteria_alpine_old_network <- analyze_taxonomic_network(bacteria_alpine_old_cor, "Bacteria_Alpine_Old")
}

# Bacteria Alpine New
if (!is.null(bacteria_alpine_new)) {
  bacteria_alpine_new_cor <- create_taxonomic_correlation_matrix(bacteria_alpine_new, threshold = 0.6)
  bacteria_alpine_new_network <- analyze_taxonomic_network(bacteria_alpine_new_cor, "Bacteria_Alpine_New")
}

# Bacteria Farming Old
if (!is.null(bacteria_farming_old)) {
  bacteria_farming_old_cor <- create_taxonomic_correlation_matrix(bacteria_farming_old, threshold = 0.6)
  bacteria_farming_old_network <- analyze_taxonomic_network(bacteria_farming_old_cor, "Bacteria_Farming_Old")
}

# Bacteria Farming New
if (!is.null(bacteria_farming_new)) {
  bacteria_farming_new_cor <- create_taxonomic_correlation_matrix(bacteria_farming_new, threshold = 0.6)
  bacteria_farming_new_network <- analyze_taxonomic_network(bacteria_farming_new_cor, "Bacteria_Farming_New")
}

# Fungi Alpine Old
if (!is.null(fungi_alpine_old)) {
  fungi_alpine_old_cor <- create_taxonomic_correlation_matrix(fungi_alpine_old, threshold = 0.6)
  fungi_alpine_old_network <- analyze_taxonomic_network(fungi_alpine_old_cor, "Fungi_Alpine_Old")
}

# Fungi Alpine New
if (!is.null(fungi_alpine_new)) {
  fungi_alpine_new_cor <- create_taxonomic_correlation_matrix(fungi_alpine_new, threshold = 0.6)
  fungi_alpine_new_network <- analyze_taxonomic_network(fungi_alpine_new_cor, "Fungi_Alpine_New")
}

# Fungi Farming Old
if (!is.null(fungi_farming_old)) {
  fungi_farming_old_cor <- create_taxonomic_correlation_matrix(fungi_farming_old, threshold = 0.6)
  fungi_farming_old_network <- analyze_taxonomic_network(fungi_farming_old_cor, "Fungi_Farming_Old")
}

# Fungi Farming New
if (!is.null(fungi_farming_new)) {
  fungi_farming_new_cor <- create_taxonomic_correlation_matrix(fungi_farming_new, threshold = 0.6)
  fungi_farming_new_network <- analyze_taxonomic_network(fungi_farming_new_cor, "Fungi_Farming_New")
}

# Function to create cross-correlation matrix between two taxonomic tables
create_taxonomic_cross_correlation_matrix <- function(table1, table2, threshold = 0.6) {
  cat("\n--- Creating cross-correlation matrix ---\n")
  cat("Table 1 dimensions:", dim(table1), "\n")
  cat("Table 2 dimensions:", dim(table2), "\n")

  tryCatch({
    # Extract abundance matrices (remove first column which contains taxonomic names)
    abundance_matrix1 <- as.matrix(table1[,-1])
    abundance_matrix2 <- as.matrix(table2[,-1])

    rownames(abundance_matrix1) <- table1[[1]]
    rownames(abundance_matrix2) <- table2[[1]]

    # Remove constant rows from both matrices
    row_sums1 <- rowSums(abundance_matrix1)
    constant_rows1 <- row_sums1 == 0 | apply(abundance_matrix1, 1, function(x) length(unique(x)) <= 1)
    if (any(constant_rows1)) {
      cat("Removing", sum(constant_rows1), "constant rows from table 1\n")
      abundance_matrix1 <- abundance_matrix1[!constant_rows1, , drop = FALSE]
    }

    row_sums2 <- rowSums(abundance_matrix2)
    constant_rows2 <- row_sums2 == 0 | apply(abundance_matrix2, 1, function(x) length(unique(x)) <= 1)
    if (any(constant_rows2)) {
      cat("Removing", sum(constant_rows2), "constant rows from table 2\n")
      abundance_matrix2 <- abundance_matrix2[!constant_rows2, , drop = FALSE]
    }

    # Check if we have enough data
    if (nrow(abundance_matrix1) <= 1 || nrow(abundance_matrix2) <= 1) {
      stop("Not enough variable rows for cross-correlation analysis")
    }

    # Get sample names and find common samples
    samples1 <- colnames(abundance_matrix1)
    samples2 <- colnames(abundance_matrix2)
    common_samples <- intersect(samples1, samples2)

    if (length(common_samples) == 0) {
      stop("No common samples found between the two tables")
    }

    cat("Found", length(common_samples), "common samples\n")

    # Subset to common samples
    abundance_matrix1 <- abundance_matrix1[, common_samples, drop = FALSE]
    abundance_matrix2 <- abundance_matrix2[, common_samples, drop = FALSE]

    cat("Final matrices: ", nrow(abundance_matrix1), "×", ncol(abundance_matrix1),
        " vs ", nrow(abundance_matrix2), "×", ncol(abundance_matrix2), "\n")

    # Calculate cross-correlations
    cat("Calculating cross-correlations...\n")
    cross_cor_matrix <- cor(t(abundance_matrix1), t(abundance_matrix2),
                           method = "spearman", use = "pairwise.complete.obs")

    # Replace any NAs with 0
    if (any(is.na(cross_cor_matrix))) {
      cat("Replacing", sum(is.na(cross_cor_matrix)), "NA values with 0\n")
      cross_cor_matrix[is.na(cross_cor_matrix)] <- 0
    }

    # Print summary before thresholding
    cat("\nBefore thresholding:\n")
    cat("Total correlations:", length(cross_cor_matrix), "\n")
    cat("Positive correlations:", sum(cross_cor_matrix > 0), "\n")
    cat("Negative correlations:", sum(cross_cor_matrix < 0), "\n")
    cat("Range:", range(cross_cor_matrix), "\n")

    # Apply threshold
    cross_cor_matrix[abs(cross_cor_matrix) < threshold] <- 0

    # Print summary after thresholding
    cat("\nAfter thresholding (|correlation| ≥", threshold, "):\n")
    cat("Total non-zero correlations:", sum(cross_cor_matrix != 0), "\n")
    cat("Positive correlations:", sum(cross_cor_matrix > 0), "\n")
    cat("Negative correlations:", sum(cross_cor_matrix < 0), "\n")

    if (sum(cross_cor_matrix != 0) > 0) {
      cat("Range of non-zero correlations:", range(cross_cor_matrix[cross_cor_matrix != 0]), "\n")
    } else {
      cat("No correlations above threshold.\n")
    }

    return(list(
      cor_matrix = cross_cor_matrix,
      taxa_names1 = rownames(abundance_matrix1),
      taxa_names2 = rownames(abundance_matrix2),
      common_samples = common_samples
    ))

  }, error = function(e) {
    cat("Error in cross-correlation calculation:", e$message, "\n")
    return(NULL)
  })
}

# Function to analyze cross-correlation network
analyze_taxonomic_cross_network <- function(cross_cor_result, group1_name, group2_name) {
  if (is.null(cross_cor_result)) {
    cat("Cannot analyze cross-network: correlation result is NULL\n")
    return(NULL)
  }

  cat("\n========== ANALYZING", group1_name, "vs", group2_name, "CROSS-NETWORK ==========\n")

  cross_cor_matrix <- cross_cor_result$cor_matrix
  taxa_names1 <- cross_cor_result$taxa_names1
  taxa_names2 <- cross_cor_result$taxa_names2

  # Check if we have any correlations
  if (sum(cross_cor_matrix != 0) == 0) {
    cat("No correlations above threshold. Cannot create cross-network.\n")
    return(NULL)
  }

  tryCatch({
    # Create bipartite network
    # First, create an edge list from the correlation matrix
    edges <- which(cross_cor_matrix != 0, arr.ind = TRUE)
    edge_list <- data.frame(
      from = taxa_names1[edges[,1]],
      to = taxa_names2[edges[,2]],
      weight = abs(cross_cor_matrix[edges]),
      correlation = cross_cor_matrix[edges],
      sign = ifelse(cross_cor_matrix[edges] > 0, "positive", "negative")
    )

    # Create igraph network
    network <- graph_from_data_frame(edge_list, directed = FALSE)

    # Add vertex attributes to distinguish between the two groups
    V(network)$type <- ifelse(V(network)$name %in% taxa_names1, group1_name, group2_name)

    # Calculate network metrics
    cat("Cross-network created with", vcount(network), "nodes and", ecount(network), "edges\n")
    cat("Group 1 (", group1_name, ") nodes:", sum(V(network)$type == group1_name), "\n")
    cat("Group 2 (", group2_name, ") nodes:", sum(V(network)$type == group2_name), "\n")

    # Node degree
    node_degrees <- degree(network)
    cat("Average degree:", mean(node_degrees), "\n")
    cat("Max degree:", max(node_degrees), "\n")

    # Centrality measures
    betweenness <- betweenness(network)
    closeness <- closeness(network)

    # Community detection
    communities <- cluster_louvain(network)
    community_count <- length(communities)
    modularity_score <- modularity(communities)

    cat("Communities detected:", community_count, "\n")
    cat("Modularity:", modularity_score, "\n")

    # Identify hub nodes (top 10% by degree)
    degree_threshold <- quantile(node_degrees, 0.9)
    hub_nodes <- names(node_degrees[node_degrees >= degree_threshold])
    cat("Hub nodes (top 10% by degree):", length(hub_nodes), "\n")

    # Create results list
    results <- list(
      network = network,
      node_degrees = node_degrees,
      betweenness = betweenness,
      closeness = closeness,
      communities = communities,
      community_count = community_count,
      modularity = modularity_score,
      hub_nodes = hub_nodes,
      edge_list = edge_list,
      group1_name = group1_name,
      group2_name = group2_name
    )

    return(results)

  }, error = function(e) {
    cat("Error in cross-network analysis:", e$message, "\n")
    return(NULL)
  })
}

# Perform cross-correlation analyses
cat("\n\n========== CROSS-CORRELATION ANALYSES ==========\n")

# Helper function to perform cross-correlation with multiple thresholds
perform_taxonomic_cross_correlation <- function(table1, table2, group1_name, group2_name) {
  cat("\n\n----- ", group1_name, " vs ", group2_name, " -----\n", sep="")

  # Try with threshold 0.6 first
  tryCatch({
    cross_cor <- create_taxonomic_cross_correlation_matrix(table1, table2, threshold = 0.6)

    if (!is.null(cross_cor) && sum(cross_cor$cor_matrix != 0) > 0) {
      cat("\nFound correlations above threshold 0.6\n")
      cross_network <- analyze_taxonomic_cross_network(cross_cor, group1_name, group2_name)
      return(cross_network)
    } else {
      cat("\nNo correlations above threshold 0.6. Trying threshold 0.5...\n")

      # Try with threshold 0.5
      cross_cor_05 <- create_taxonomic_cross_correlation_matrix(table1, table2, threshold = 0.5)

      if (!is.null(cross_cor_05) && sum(cross_cor_05$cor_matrix != 0) > 0) {
        cat("\nFound correlations above threshold 0.5\n")
        cross_network <- analyze_taxonomic_cross_network(cross_cor_05,
                                                        paste(group1_name, "(0.5)"),
                                                        paste(group2_name, "(0.5)"))
        return(cross_network)
      } else {
        cat("\nNo correlations above threshold 0.5. Trying threshold 0.4...\n")

        # Try with threshold 0.4
        cross_cor_04 <- create_taxonomic_cross_correlation_matrix(table1, table2, threshold = 0.4)

        if (!is.null(cross_cor_04) && sum(cross_cor_04$cor_matrix != 0) > 0) {
          cat("\nFound correlations above threshold 0.4\n")
          cross_network <- analyze_taxonomic_cross_network(cross_cor_04,
                                                          paste(group1_name, "(0.4)"),
                                                          paste(group2_name, "(0.4)"))
          return(cross_network)
        } else {
          cat("\nNo correlations above threshold 0.4 found.\n")
          return(NULL)
        }
      }
    }
  }, error = function(e) {
    cat("Error in cross-correlation analysis:", e$message, "\n")
    return(NULL)
  })
}

# Perform the 4 cross-correlations for bacteria vs fungi in each environment/time combination

# 1. Bacteria Alpine Old vs Fungi Alpine Old
if (!is.null(bacteria_alpine_old) && !is.null(fungi_alpine_old)) {
  bacteria_fungi_alpine_old_network <- perform_taxonomic_cross_correlation(
    bacteria_alpine_old, fungi_alpine_old,
    "Bacteria_Alpine_Old", "Fungi_Alpine_Old"
  )
}

# 2. Bacteria Alpine New vs Fungi Alpine New
if (!is.null(bacteria_alpine_new) && !is.null(fungi_alpine_new)) {
  bacteria_fungi_alpine_new_network <- perform_taxonomic_cross_correlation(
    bacteria_alpine_new, fungi_alpine_new,
    "Bacteria_Alpine_New", "Fungi_Alpine_New"
  )
}

# 3. Bacteria Farming Old vs Fungi Farming Old
if (!is.null(bacteria_farming_old) && !is.null(fungi_farming_old)) {
  bacteria_fungi_farming_old_network <- perform_taxonomic_cross_correlation(
    bacteria_farming_old, fungi_farming_old,
    "Bacteria_Farming_Old", "Fungi_Farming_Old"
  )
}

# 4. Bacteria Farming New vs Fungi Farming New
if (!is.null(bacteria_farming_new) && !is.null(fungi_farming_new)) {
  bacteria_fungi_farming_new_network <- perform_taxonomic_cross_correlation(
    bacteria_farming_new, fungi_farming_new,
    "Bacteria_Farming_New", "Fungi_Farming_New"
  )
}

cat("\n\n========== ANALYSIS COMPLETED ==========\n")
cat("Taxonomic level:", TAXONOMIC_LEVEL, "\n")
cat("All individual and cross-correlation network analyses completed!\n")
