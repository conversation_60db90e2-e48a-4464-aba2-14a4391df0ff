modularity = mod_value
))
}
# Create filtered datasets for old and new samples
cat("\n========== FILTERING SAMPLES BY ENVIRONMENT ==========\n")
# Bacteria Alpine
bacteria_alpine_old <- filter_samples_by_environment(bacteria_alpine_otu, bacteria_alpine_meta, "old")
bacteria_alpine_new <- filter_samples_by_environment(bacteria_alpine_otu, bacteria_alpine_meta, "new")
# Bacteria Farming
bacteria_farming_old <- filter_samples_by_environment(bacteria_farming_otu, bacteria_farming_meta, "old")
bacteria_farming_new <- filter_samples_by_environment(bacteria_farming_otu, bacteria_farming_meta, "new")
# Fungi Alpine
fungi_alpine_old <- filter_samples_by_environment(fungi_alpine_otu, fungi_alpine_meta, "old")
fungi_alpine_new <- filter_samples_by_environment(fungi_alpine_otu, fungi_alpine_meta, "new")
# Fungi Farming
fungi_farming_old <- filter_samples_by_environment(fungi_farming_otu, fungi_farming_meta, "old")
fungi_farming_new <- filter_samples_by_environment(fungi_farming_otu, fungi_farming_meta, "new")
# Analyze each group with threshold 0.6 as requested
cat("\n========== INDIVIDUAL NETWORK ANALYSES ==========\n")
# Bacteria Alpine Old (use smaller max_otus for large datasets)
if (!is.null(bacteria_alpine_old)) {
bacteria_alpine_old_cor <- create_correlation_matrix(bacteria_alpine_old, threshold = 0.6, max_otus = 3000)
bacteria_alpine_old_network <- analyze_network(bacteria_alpine_old_cor, bacteria_alpine_tax, "Bacteria_Alpine_Old")
}
# Bacteria Alpine New
if (!is.null(bacteria_alpine_new)) {
bacteria_alpine_new_cor <- create_correlation_matrix(bacteria_alpine_new, threshold = 0.6, max_otus = 3000)
bacteria_alpine_new_network <- analyze_network(bacteria_alpine_new_cor, bacteria_alpine_tax, "Bacteria_Alpine_New")
}
# Bacteria Farming Old
if (!is.null(bacteria_farming_old)) {
bacteria_farming_old_cor <- create_correlation_matrix(bacteria_farming_old, threshold = 0.6, max_otus = 3000)
bacteria_farming_old_network <- analyze_network(bacteria_farming_old_cor, bacteria_farming_tax, "Bacteria_Farming_Old")
}
# Bacteria Farming New
if (!is.null(bacteria_farming_new)) {
bacteria_farming_new_cor <- create_correlation_matrix(bacteria_farming_new, threshold = 0.6, max_otus = 3000)
bacteria_farming_new_network <- analyze_network(bacteria_farming_new_cor, bacteria_farming_tax, "Bacteria_Farming_New")
}
# Fungi Alpine Old (fungi datasets are smaller, can handle more OTUs)
if (!is.null(fungi_alpine_old)) {
fungi_alpine_old_cor <- create_correlation_matrix(fungi_alpine_old, threshold = 0.6, max_otus = 6000)
fungi_alpine_old_network <- analyze_network(fungi_alpine_old_cor, fungi_alpine_tax, "Fungi_Alpine_Old")
}
# Fungi Alpine New
if (!is.null(fungi_alpine_new)) {
fungi_alpine_new_cor <- create_correlation_matrix(fungi_alpine_new, threshold = 0.6, max_otus = 6000)
fungi_alpine_new_network <- analyze_network(fungi_alpine_new_cor, fungi_alpine_tax, "Fungi_Alpine_New")
}
# Fungi Farming Old
if (!is.null(fungi_farming_old)) {
fungi_farming_old_cor <- create_correlation_matrix(fungi_farming_old, threshold = 0.6, max_otus = 6000)
fungi_farming_old_network <- analyze_network(fungi_farming_old_cor, fungi_farming_tax, "Fungi_Farming_Old")
}
# Fungi Farming New
if (!is.null(fungi_farming_new)) {
fungi_farming_new_cor <- create_correlation_matrix(fungi_farming_new, threshold = 0.6, max_otus = 6000)
fungi_farming_new_network <- analyze_network(fungi_farming_new_cor, fungi_farming_tax, "Fungi_Farming_New")
}
# Compare network properties
cat("\n\n========== NETWORK COMPARISON ==========\n")
tryCatch({
# Create a list to store network data
network_data <- list()
# Helper function to add network data
add_network_data <- function(network_obj, otu_count, group_name) {
if (exists(deparse(substitute(network_obj))) && !is.null(network_obj)) {
# Calculate normalized metrics
nodes_count <- vcount(network_obj$network)
edges_count <- ecount(network_obj$network)
network_data[[group_name]] <<- list(
# Raw metrics
Nodes = nodes_count,
Edges = edges_count,
Avg_Degree = mean(network_obj$degree),
Max_Degree = max(network_obj$degree),
Avg_Betweenness = mean(network_obj$betweenness),
Avg_Closeness = mean(network_obj$closeness),
Communities = network_obj$community_count,
Modularity = network_obj$modularity,
Hub_Nodes = length(network_obj$hub_nodes),
# Normalized metrics (by OTU count)
Norm_Nodes = nodes_count / otu_count,
Norm_Edges = edges_count / (otu_count * (otu_count - 1) / 2),
Norm_Avg_Degree = mean(network_obj$degree) / otu_count,
Norm_Max_Degree = max(network_obj$degree) / otu_count,
Norm_Communities = network_obj$community_count / nodes_count,
Network_Density = edge_density(network_obj$network)
)
}
}
# Add all network data
if (exists("bacteria_alpine_old_network")) {
add_network_data(bacteria_alpine_old_network, nrow(bacteria_alpine_otu), "Bacteria_Alpine_Old")
}
if (exists("bacteria_alpine_new_network")) {
add_network_data(bacteria_alpine_new_network, nrow(bacteria_alpine_otu), "Bacteria_Alpine_New")
}
if (exists("bacteria_farming_old_network")) {
add_network_data(bacteria_farming_old_network, nrow(bacteria_farming_otu), "Bacteria_Farming_Old")
}
if (exists("bacteria_farming_new_network")) {
add_network_data(bacteria_farming_new_network, nrow(bacteria_farming_otu), "Bacteria_Farming_New")
}
if (exists("fungi_alpine_old_network")) {
add_network_data(fungi_alpine_old_network, nrow(fungi_alpine_otu), "Fungi_Alpine_Old")
}
if (exists("fungi_alpine_new_network")) {
add_network_data(fungi_alpine_new_network, nrow(fungi_alpine_otu), "Fungi_Alpine_New")
}
if (exists("fungi_farming_old_network")) {
add_network_data(fungi_farming_old_network, nrow(fungi_farming_otu), "Fungi_Farming_Old")
}
if (exists("fungi_farming_new_network")) {
add_network_data(fungi_farming_new_network, nrow(fungi_farming_otu), "Fungi_Farming_New")
}
# Check if we have any network data
if (length(network_data) > 0) {
# Create two data frames: one for raw metrics and one for normalized metrics
# Create separate data frames for raw and normalized metrics
# We'll keep them separate to avoid column mismatch issues
# Raw metrics
raw_networks <- data.frame(
Group = names(network_data),
Nodes = sapply(network_data, function(x) x$Nodes),
Edges = sapply(network_data, function(x) x$Edges),
Avg_Degree = sapply(network_data, function(x) x$Avg_Degree),
Max_Degree = sapply(network_data, function(x) x$Max_Degree),
Avg_Betweenness = sapply(network_data, function(x) x$Avg_Betweenness),
Avg_Closeness = sapply(network_data, function(x) x$Avg_Closeness),
Communities = sapply(network_data, function(x) x$Communities),
Modularity = sapply(network_data, function(x) x$Modularity),
Hub_Nodes = sapply(network_data, function(x) x$Hub_Nodes)
)
# Normalized metrics
norm_networks <- data.frame(
Group = names(network_data),
Norm_Nodes = sapply(network_data, function(x) ifelse(is.null(x$Norm_Nodes), NA, x$Norm_Nodes)),
Norm_Edges = sapply(network_data, function(x) ifelse(is.null(x$Norm_Edges), NA, x$Norm_Edges)),
Norm_Avg_Degree = sapply(network_data, function(x) ifelse(is.null(x$Norm_Avg_Degree), NA, x$Norm_Avg_Degree)),
Norm_Max_Degree = sapply(network_data, function(x) ifelse(is.null(x$Norm_Max_Degree), NA, x$Norm_Max_Degree)),
Norm_Communities = sapply(network_data, function(x) ifelse(is.null(x$Norm_Communities), NA, x$Norm_Communities)),
Network_Density = sapply(network_data, function(x) ifelse(is.null(x$Network_Density), NA, x$Network_Density))
)
# Save both to separate CSV files
write.csv(raw_networks, "network_comparison_raw.csv", row.names = FALSE)
write.csv(norm_networks, "network_comparison_normalized.csv", row.names = FALSE)
cat("Network comparison saved to network_comparison_raw.csv and network_comparison_normalized.csv\n")
# Create visualizations of network metrics
# 1. Average Degree comparison (both raw and normalized)
# Raw
p1a <- ggplot(raw_networks, aes(x = Group, y = Avg_Degree)) +
geom_bar(stat = "identity", fill = "steelblue") +
theme_minimal() +
ggtitle("Average Degree by Group (Raw)")
ggsave("avg_degree_comparison_raw.pdf", p1a)
p1b <- ggplot(norm_networks, aes(x = Group, y = Norm_Avg_Degree)) +
geom_bar(stat = "identity", fill = "lightblue") +
theme_minimal() +
ggtitle("Average Degree by Group (Normalized)")
ggsave("avg_degree_comparison_normalized.pdf", p1b)
# 2. Network Size comparison (Nodes and Edges)
# Raw
network_size_raw <- data.frame(
Group = rep(raw_networks$Group, 2),
Metric = c(rep("Nodes", nrow(raw_networks)), rep("Edges", nrow(raw_networks))),
Value = c(raw_networks$Nodes, raw_networks$Edges)
)
p2a <- ggplot(network_size_raw, aes(x = Group, y = Value, fill = Metric)) +
geom_bar(stat = "identity", position = "dodge") +
theme_minimal() +
ggtitle("Network Size Comparison (Raw)") +
scale_fill_manual(values = c("Nodes" = "darkgreen", "Edges" = "darkred"))
ggsave("network_size_comparison_raw.pdf", p2a)
# Normalized
network_size_norm <- data.frame(
Group = rep(norm_networks$Group, 2),
Metric = c(rep("Norm_Nodes", nrow(norm_networks)), rep("Norm_Edges", nrow(norm_networks))),
Value = c(norm_networks$Norm_Nodes, norm_networks$Norm_Edges)
)
p2b <- ggplot(network_size_norm, aes(x = Group, y = Value, fill = Metric)) +
geom_bar(stat = "identity", position = "dodge") +
theme_minimal() +
ggtitle("Network Size Comparison (Normalized)") +
scale_fill_manual(values = c("Norm_Nodes" = "lightgreen", "Norm_Edges" = "pink"))
ggsave("network_size_comparison_normalized.pdf", p2b)
# 3. Community Structure comparison
# Raw
community_data_raw <- data.frame(
Group = raw_networks$Group,
Communities = raw_networks$Communities,
Modularity = raw_networks$Modularity
)
p3a <- ggplot(community_data_raw, aes(x = Group)) +
geom_bar(aes(y = Communities), stat = "identity", fill = "purple") +
geom_line(aes(y = Modularity * max(Communities) * 2, group = 1), color = "orange", size = 1.5) +
geom_point(aes(y = Modularity * max(Communities) * 2), color = "orange", size = 3) +
scale_y_continuous(name = "Number of Communities",
sec.axis = sec_axis(~./(max(community_data_raw$Communities) * 2), name = "Modularity")) +
theme_minimal() +
ggtitle("Community Structure Comparison (Raw)")
ggsave("community_comparison_raw.pdf", p3a)
# Normalized
community_data_norm <- data.frame(
Group = norm_networks$Group,
Norm_Communities = norm_networks$Norm_Communities,
Network_Density = norm_networks$Network_Density
)
p3b <- ggplot(community_data_norm, aes(x = Group)) +
geom_bar(aes(y = Norm_Communities), stat = "identity", fill = "lavender") +
geom_line(aes(y = Network_Density * max(Norm_Communities) * 2, group = 1), color = "red", size = 1.5) +
geom_point(aes(y = Network_Density * max(Norm_Communities) * 2), color = "red", size = 3) +
scale_y_continuous(name = "Normalized Communities",
sec.axis = sec_axis(~./(max(community_data_norm$Norm_Communities) * 2), name = "Network Density")) +
theme_minimal() +
ggtitle("Community Structure Comparison (Normalized)")
ggsave("community_comparison_normalized.pdf", p3b)
cat("Network visualizations saved to PDF files\n")
# Add statistical tests to compare network properties
cat("\n\n========== STATISTICAL TESTS ==========\n")
# Add community-locality correlation analysis
cat("\n========== COMMUNITY-LOCALITY CORRELATION ANALYSIS ==========\n")
# Function to analyze community-locality associations
analyze_community_locality <- function(network_obj, metadata_file, group_name) {
tryCatch({
# Check if network object exists and has community data
if (is.null(network_obj) || !("community" %in% names(network_obj))) {
cat("No community data available for", group_name, "\n")
return(NULL)
}
# Try to read metadata
metadata <- NULL
if (file.exists(metadata_file)) {
if (grepl("\\.xlsx$", metadata_file)) {
metadata <- readxl::read_excel(metadata_file)
} else if (grepl("\\.csv$", metadata_file)) {
metadata <- read.csv(metadata_file)
} else {
cat("Unsupported metadata file format:", metadata_file, "\n")
return(NULL)
}
} else {
cat("Metadata file not found:", metadata_file, "\n")
return(NULL)
}
if (is.null(metadata) || nrow(metadata) == 0) {
cat("No metadata available for", group_name, "\n")
return(NULL)
}
# Get community assignments
community_data <- data.frame(
OTU = V(network_obj$network)$name,
Community = network_obj$community,
stringsAsFactors = FALSE
)
# Get sample information from the network
sample_data <- data.frame(
Sample = colnames(network_obj$otu_table)[-1],  # Exclude first column (OTU IDs)
stringsAsFactors = FALSE
)
# Merge with metadata to get locality information
# First, find the column that contains sample IDs in metadata
sample_col <- NULL
for (col in colnames(metadata)) {
if (any(sample_data$Sample %in% metadata[[col]])) {
sample_col <- col
break
}
}
if (is.null(sample_col)) {
cat("Could not find matching sample IDs in metadata for", group_name, "\n")
return(NULL)
}
# Find locality column in metadata
locality_col <- NULL
possible_cols <- c("Locality", "Location", "Site", "SamplingLocation", "Sampling_Location", "Sampling_Site")
for (col in possible_cols) {
if (col %in% colnames(metadata)) {
locality_col <- col
break
}
}
if (is.null(locality_col)) {
cat("Could not find locality column in metadata for", group_name, "\n")
return(NULL)
}
# Merge sample data with metadata
merged_data <- merge(sample_data, metadata, by.x = "Sample", by.y = sample_col)
# Count OTUs per community
community_counts <- table(community_data$Community)
# Get OTU presence per sample
otu_presence <- network_obj$otu_table[, -1]  # Remove OTU ID column
rownames(otu_presence) <- network_obj$otu_table[, 1]
# Convert to presence/absence (1/0)
otu_presence[otu_presence > 0] <- 1
# For each community, calculate its prevalence in each locality
communities <- unique(community_data$Community)
localities <- unique(merged_data[[locality_col]])
# Create a matrix to store community prevalence by locality
community_by_locality <- matrix(0, nrow = length(communities), ncol = length(localities))
rownames(community_by_locality) <- communities
colnames(community_by_locality) <- localities
# For each community
for (comm in communities) {
# Get OTUs in this community
comm_otus <- community_data$OTU[community_data$Community == comm]
# For each locality
for (loc in localities) {
# Get samples from this locality
loc_samples <- merged_data$Sample[merged_data[[locality_col]] == loc]
# Calculate the average presence of community OTUs in this locality
if (length(loc_samples) > 0 && length(comm_otus) > 0) {
# Get columns corresponding to these samples
sample_cols <- match(loc_samples, colnames(otu_presence))
sample_cols <- sample_cols[!is.na(sample_cols)]
if (length(sample_cols) > 0) {
# Get rows corresponding to community OTUs
otu_rows <- match(comm_otus, rownames(otu_presence))
otu_rows <- otu_rows[!is.na(otu_rows)]
if (length(otu_rows) > 0) {
# Calculate average presence
presence_subset <- otu_presence[otu_rows, sample_cols, drop = FALSE]
avg_presence <- sum(presence_subset) / (length(otu_rows) * length(sample_cols))
community_by_locality[as.character(comm), loc] <- avg_presence
}
}
}
}
}
# Perform chi-square test for association between communities and localities
# Convert the matrix to a data frame for analysis
community_locality_df <- as.data.frame.table(community_by_locality)
names(community_locality_df) <- c("Community", "Locality", "Prevalence")
# Only keep non-zero entries
community_locality_df <- community_locality_df[community_locality_df$Prevalence > 0, ]
# Create a contingency table
contingency_table <- xtabs(Prevalence ~ Community + Locality, data = community_locality_df)
# Perform chi-square test
chi_test <- chisq.test(contingency_table, simulate.p.value = TRUE)
# Save the community-locality matrix
write.csv(community_by_locality,
paste0("community_locality_", tolower(group_name), ".csv"))
# Create a heatmap visualization
pdf(paste0("community_locality_heatmap_", tolower(group_name), ".pdf"),
width = max(8, length(localities) * 0.5),
height = max(8, length(communities) * 0.3))
# Normalize for better visualization
normalized_matrix <- community_by_locality
for (i in 1:nrow(normalized_matrix)) {
row_max <- max(normalized_matrix[i, ])
if (row_max > 0) {
normalized_matrix[i, ] <- normalized_matrix[i, ] / row_max
}
}
# Create heatmap
heatmap(normalized_matrix,
col = colorRampPalette(c("white", "yellow", "orange", "red"))(100),
main = paste("Community-Locality Association for", group_name),
xlab = "Locality",
ylab = "Community")
dev.off()
# Return results
return(list(
chi_test = chi_test,
community_by_locality = community_by_locality,
community_locality_df = community_locality_df
))
}, error = function(e) {
cat("Error in community-locality analysis for", group_name, ":", e$message, "\n")
return(NULL)
})
}
# Analyze each network
if (exists("fungi_network") && !is.null(fungi_network)) {
cat("\nAnalyzing community-locality associations for Fungi\n")
fungi_locality <- analyze_community_locality(fungi_network, "metadata_fungi.xlsx", "Fungi")
if (!is.null(fungi_locality) && !is.null(fungi_locality$chi_test)) {
cat("Chi-square test p-value:", fungi_locality$chi_test$p.value, "\n")
cat("Significant association:", ifelse(fungi_locality$chi_test$p.value < 0.05, "Yes", "No"), "\n")
}
}
if (exists("bacteria_network") && !is.null(bacteria_network)) {
cat("\nAnalyzing community-locality associations for Bacteria\n")
bacteria_locality <- analyze_community_locality(bacteria_network, "metadata_bacteria.xlsx", "Bacteria")
if (!is.null(bacteria_locality) && !is.null(bacteria_locality$chi_test)) {
cat("Chi-square test p-value:", bacteria_locality$chi_test$p.value, "\n")
cat("Significant association:", ifelse(bacteria_locality$chi_test$p.value < 0.05, "Yes", "No"), "\n")
}
}
if (exists("metazoa_network") && !is.null(metazoa_network)) {
cat("\nAnalyzing community-locality associations for Metazoa\n")
metazoa_locality <- analyze_community_locality(metazoa_network, "metadata_metazoa.xlsx", "Metazoa")
if (!is.null(metazoa_locality) && !is.null(metazoa_locality$chi_test)) {
cat("Chi-square test p-value:", metazoa_locality$chi_test$p.value, "\n")
cat("Significant association:", ifelse(metazoa_locality$chi_test$p.value < 0.05, "Yes", "No"), "\n")
}
}
# Add standard statistical tests to compare network properties
cat("\n========== NETWORK PROPERTY COMPARISON TESTS ==========\n")
# Only perform tests if we have at least 2 networks
if (nrow(raw_networks) >= 2) {
# Create a function to perform statistical tests on network properties
perform_network_tests <- function() {
# Create a data frame to store test results
test_results <- data.frame(
Comparison = character(),
Property = character(),
Test = character(),
P_Value = numeric(),
Significant = character(),
stringsAsFactors = FALSE
)
# Get all pairwise combinations of networks
network_pairs <- combn(raw_networks$Group, 2, simplify = FALSE)
# Properties to test
properties <- c("Avg_Degree", "Avg_Betweenness", "Avg_Closeness", "Modularity")
# Perform tests for each pair and property
for (pair in network_pairs) {
group1 <- pair[1]
group2 <- pair[2]
comparison_name <- paste(group1, "vs", group2)
# Get network objects
network1 <- NULL
network2 <- NULL
if (group1 == "Fungi" && exists("fungi_network")) network1 <- fungi_network
if (group1 == "Bacteria" && exists("bacteria_network")) network1 <- bacteria_network
if (group1 == "Metazoa" && exists("metazoa_network")) network1 <- metazoa_network
if (group2 == "Fungi" && exists("fungi_network")) network2 <- fungi_network
if (group2 == "Bacteria" && exists("bacteria_network")) network2 <- bacteria_network
if (group2 == "Metazoa" && exists("metazoa_network")) network2 <- metazoa_network
if (!is.null(network1) && !is.null(network2)) {
# Compare network properties
cat("\nComparing", group1, "vs", group2, "\n")
# 1. Compare degree distributions using Kolmogorov-Smirnov test
tryCatch({
ks_test <- ks.test(network1$degree, network2$degree)
p_value <- ks_test$p.value
significant <- ifelse(p_value < 0.05, "Yes", "No")
test_results <- rbind(test_results, data.frame(
Comparison = comparison_name,
Property = "Degree Distribution",
Test = "Kolmogorov-Smirnov",
P_Value = p_value,
Significant = significant,
stringsAsFactors = FALSE
))
cat("Degree Distribution (KS test): p-value =", p_value,
ifelse(significant == "Yes", "(Significant)", "(Not Significant)"), "\n")
}, error = function(e) {
cat("Error in KS test for degree distribution:", e$message, "\n")
})
# 2. Compare betweenness distributions
tryCatch({
ks_test <- ks.test(network1$betweenness, network2$betweenness)
p_value <- ks_test$p.value
significant <- ifelse(p_value < 0.05, "Yes", "No")
test_results <- rbind(test_results, data.frame(
Comparison = comparison_name,
Property = "Betweenness Distribution",
Test = "Kolmogorov-Smirnov",
P_Value = p_value,
Significant = significant,
stringsAsFactors = FALSE
))
cat("Betweenness Distribution (KS test): p-value =", p_value,
ifelse(significant == "Yes", "(Significant)", "(Not Significant)"), "\n")
}, error = function(e) {
cat("Error in KS test for betweenness distribution:", e$message, "\n")
})
# 3. Compare network density
density1 <- edge_density(network1$network)
density2 <- edge_density(network2$network)
cat("Network Density:", group1, "=", density1, ",", group2, "=", density2, "\n")
# 4. Compare modularity
mod1 <- network1$modularity
mod2 <- network2$modularity
cat("Modularity:", group1, "=", mod1, ",", group2, "=", mod2, "\n")
}
}
# Save test results to CSV
write.csv(test_results, "network_statistical_tests.csv", row.names = FALSE)
cat("Statistical test results saved to network_statistical_tests.csv\n")
return(test_results)
}
# Run the tests
test_results <- perform_network_tests()
# Create visualization of test results
if (nrow(test_results) > 0) {
# Create a heatmap of p-values
test_results$NegLog10P <- -log10(test_results$P_Value)
p4 <- ggplot(test_results, aes(x = Comparison, y = Property, fill = NegLog10P)) +
geom_tile() +
scale_fill_gradient(low = "white", high = "red",
name = "-log10(p-value)",
guide = guide_colorbar(title.position = "top")) +
geom_text(aes(label = ifelse(Significant == "Yes", "*", "")),
color = "black", size = 8) +
theme_minimal() +
theme(axis.text.x = element_text(angle = 45, hjust = 1)) +
ggtitle("Statistical Significance of Network Property Differences")
ggsave("network_statistical_tests.pdf", p4, width = 8, height = 6)
cat("Statistical test visualization saved to network_statistical_tests.pdf\n")
}
} else {
cat("Need at least 2 networks to perform statistical tests\n")
}
} else {
cat("No network data available for comparison\n")
}
}, error = function(e) {
cat("Error in network comparison:", e$message, "\n")
})
