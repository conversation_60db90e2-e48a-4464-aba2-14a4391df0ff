}, error = function(e) {
cat("Error in", algorithm_name, "algorithm:", e$message, "\n")
return(NULL)
})
}
# Try different community detection algorithms
# 1. Fast Greedy algorithm (works well for medium-sized networks)
if (vcount(network) < 5000) {
fg_result <- try_community_detection("Fast Greedy", cluster_fast_greedy, weights = abs(E(network)$weight))
if (!is.null(fg_result) && fg_result$modularity > best_modularity) {
best_modularity <- fg_result$modularity
communities <- fg_result$communities
best_algorithm <- "Fast Greedy"
}
}
# 2. Louvain algorithm (works well for larger networks)
louvain_result <- try_community_detection("Louvain", cluster_louvain, weights = abs(E(network)$weight))
if (!is.null(louvain_result) && louvain_result$modularity > best_modularity) {
best_modularity <- louvain_result$modularity
communities <- louvain_result$communities
best_algorithm <- "Louvain"
}
# 3. Walktrap algorithm (works well for most networks)
wt_result <- try_community_detection("Walktrap", cluster_walktrap, weights = abs(E(network)$weight))
if (!is.null(wt_result) && wt_result$modularity > best_modularity) {
best_modularity <- wt_result$modularity
communities <- wt_result$communities
best_algorithm <- "Walktrap"
}
# 4. Label Propagation algorithm (fast but less stable)
lp_result <- try_community_detection("Label Propagation", cluster_label_prop, weights = abs(E(network)$weight))
if (!is.null(lp_result) && lp_result$modularity > best_modularity) {
best_modularity <- lp_result$modularity
communities <- lp_result$communities
best_algorithm <- "Label Propagation"
}
# 5. Infomap algorithm (good for finding small communities)
if (vcount(network) < 3000) {
infomap_result <- try_community_detection("Infomap", cluster_infomap, e.weights = abs(E(network)$weight))
if (!is.null(infomap_result) && infomap_result$modularity > best_modularity) {
best_modularity <- infomap_result$modularity
communities <- infomap_result$communities
best_algorithm <- "Infomap"
}
}
# If all algorithms failed, create a fallback community structure
if (is.null(communities)) {
cat("All community detection algorithms failed. Creating fallback community structure.\n")
communities <- make_clusters(network, membership = rep(1, vcount(network)))
best_algorithm <- "Fallback (single community)"
best_modularity <- 0
} else {
cat("Selected", best_algorithm, "algorithm with modularity", best_modularity, "\n")
}
# Network analysis results
cat("\nNetwork Analysis Results for", group_name, "\n")
cat("Number of nodes:", vcount(network), "\n")
cat("Number of edges:", ecount(network), "\n")
cat("Positive edges:", sum(E(network)$weight > 0), "\n")
cat("Negative edges:", sum(E(network)$weight < 0), "\n")
cat("Network density:", edge_density(network), "\n")
# Handle community statistics safely
community_count <- 0
mod_value <- 0
# Initialize community count and modularity
community_count <- 1
mod_value <- 0
tryCatch({
# For newer versions of igraph, membership() is a function not an attribute
if (is.function(membership)) {
comm_membership <- membership(communities)
} else if (inherits(communities, "communities")) {
comm_membership <- communities$membership
} else {
# Direct access
comm_membership <- communities
}
# Convert to numeric vector if needed
if (is.list(comm_membership)) {
comm_membership <- unlist(comm_membership)
}
# Ensure it's a numeric vector
comm_membership <- as.numeric(comm_membership)
community_count <- length(unique(comm_membership))
cat("Number of communities:", community_count, "\n")
# Calculate modularity safely
tryCatch({
mod_value <- modularity(network, comm_membership)
cat("Modularity:", mod_value, "\n")
}, error = function(e) {
cat("Error calculating modularity:", e$message, "\n")
mod_value <- 0
})
}, error = function(e) {
cat("Error getting community statistics:", e$message, "\n")
cat("Skipping community statistics\n")
community_count <- 1
mod_value <- 0
})
# Create network plot
plot_title <- paste("Network for", group_name)
pdf(paste0("network_", tolower(group_name), ".pdf"))
# Edge colors based on correlation sign
edge_colors <- ifelse(E(network)$weight > 0, "blue", "red")
edge_widths <- abs(E(network)$weight) * 2  # Scale edge widths by correlation strength
# Store edge weights for potential filtering
E(network)$abs_weight <- abs(E(network)$weight)
# Calculate node properties for visualization
# Degree centrality (number of connections)
node_degrees <- degree(network)
# Betweenness centrality (importance as bridge between communities)
node_betweenness <- betweenness(network, normalized = TRUE)
# Scale node sizes based on degree (more connections = larger node)
# Use log scale to prevent extremely large nodes
min_size <- 2
max_size <- 10
if (max(node_degrees) > min(node_degrees)) {
node_sizes <- min_size + (max_size - min_size) *
(node_degrees - min(node_degrees)) / (max(node_degrees) - min(node_degrees))
} else {
node_sizes <- rep(min_size, length(node_degrees))
}
# Identify hub nodes (nodes with degree > 6 as requested)
# Find nodes with degree greater than 6
hub_nodes <- which(node_degrees > 6)
# Sort hub nodes by degree in descending order
hub_nodes <- hub_nodes[order(node_degrees[hub_nodes], decreasing = TRUE)]
cat("Selected", length(hub_nodes), "hub nodes with degree > 6\n")
# Create node labels (only for hub nodes)
node_labels <- rep(NA, vcount(network))
# Export hub node taxonomy to a separate file
if (length(hub_nodes) > 0) {
# Create a data frame to store hub node information
hub_data <- data.frame(
Node_ID = hub_nodes,
Group = rep(group_name, length(hub_nodes)),
Degree = node_degrees[hub_nodes],
Betweenness = node_betweenness[hub_nodes],
OTU_ID = NA,
stringsAsFactors = FALSE
)
# Add OTU ID information if available
if (!is.null(taxonomy_data) && nrow(taxonomy_data) > 0) {
# Get the original indices (before filtering constant rows)
if (exists("filtered_indices") && length(filtered_indices) > 0) {
# Map network node IDs back to original OTU indices
original_indices <- filtered_indices[as.numeric(hub_nodes)]
# Check if indices are within bounds
valid_indices <- original_indices <= nrow(taxonomy_data)
if (any(valid_indices)) {
# Add OTU ID (first column of taxonomy data)
for (i in seq_along(hub_nodes)) {
if (valid_indices[i]) {
idx <- original_indices[i]
hub_data$OTU_ID[i] <- as.character(taxonomy_data[idx, 1])
}
}
# Get taxonomy information for hub nodes (for labels)
if ("Genus" %in% colnames(taxonomy_data)) {
hub_labels <- taxonomy_data$Genus[original_indices[valid_indices]]
} else if ("genus" %in% colnames(taxonomy_data)) {
hub_labels <- taxonomy_data$genus[original_indices[valid_indices]]
} else {
# Use the first column as fallback
hub_labels <- as.character(taxonomy_data[original_indices[valid_indices], 1])
}
# Assign labels to hub nodes (for potential use)
node_labels[hub_nodes[valid_indices]] <- hub_labels
}
}
}
# If no taxonomy data or mapping failed, use node IDs
if (all(is.na(node_labels[hub_nodes]))) {
node_labels[hub_nodes] <- paste("Node", hub_nodes)
}
# Sort hub nodes by degree (descending)
hub_data <- hub_data[order(hub_data$Degree, decreasing = TRUE), ]
# Save hub node information to a CSV file
hub_file <- paste0("hub_nodes_", tolower(group_name), ".csv")
write.csv(hub_data, hub_file, row.names = FALSE)
cat("Hub node information saved to", hub_file, "\n")
}
# Use a consistent approach for visualization across all groups
# First create a basic plot with node sizes based on degree but no labels
plot(network,
vertex.size = node_sizes,
vertex.label = NA,  # Remove labels as requested
edge.color = edge_colors,
edge.width = edge_widths,
main = plot_title)
# Add community visualization with improved coloring
tryCatch({
# Get community membership safely using the same approach as above
if (is.function(membership)) {
comm_membership <- membership(communities)
} else if (inherits(communities, "communities")) {
comm_membership <- communities$membership
} else {
# Direct access
comm_membership <- communities
}
# Convert to numeric vector if needed
if (is.list(comm_membership)) {
comm_membership <- unlist(comm_membership)
}
# Ensure it's a numeric vector
comm_membership <- as.numeric(comm_membership)
# Get unique communities and count them
unique_communities <- sort(unique(comm_membership))
community_count <- length(unique_communities)
cat("Found", community_count, "communities\n")
# Generate better colors for communities
# Use a more visually distinct color palette for better differentiation
if (community_count <= 8) {
# Use ColorBrewer palette for small number of communities
community_colors <- c("#E41A1C", "#377EB8", "#4DAF4A", "#984EA3",
"#FF7F00", "#FFFF33", "#A65628", "#F781BF")
community_colors <- community_colors[1:community_count]
} else if (community_count <= 20) {
# Use a larger distinct palette for medium number of communities
community_colors <- c("#E41A1C", "#377EB8", "#4DAF4A", "#984EA3",
"#FF7F00", "#FFFF33", "#A65628", "#F781BF",
"#66C2A5", "#FC8D62", "#8DA0CB", "#E78AC3",
"#A6D854", "#FFD92F", "#E5C494", "#B3B3B3",
"#8DD3C7", "#BEBADA", "#FB8072", "#80B1D3")
community_colors <- community_colors[1:community_count]
} else {
# For many communities, use a continuous color palette
community_colors <- colorRampPalette(c("#2166AC", "#67A9CF", "#D1E5F0",
"#F7F7F7", "#FDDBC7", "#EF8A62",
"#B2182B"))(community_count)
}
# Create a mapping from community IDs to color indices
color_mapping <- setNames(seq_along(unique_communities), unique_communities)
# Color vertices by community (with error handling)
if (length(comm_membership) == vcount(network)) {
# Map each community to its color
V(network)$color <- community_colors[color_mapping[comm_membership]]
V(network)$community <- comm_membership  # Store community membership for reference
} else {
# If lengths don't match, use a default coloring
V(network)$color <- "lightblue"
cat("Warning: Community membership length doesn't match network size\n")
}
# Plot again with community colors and node sizes but no labels
plot(network,
vertex.size = node_sizes,
vertex.label = NA,  # Remove labels as requested
vertex.color = V(network)$color,
edge.color = edge_colors,
edge.width = edge_widths,
layout = layout_with_fr(network),  # Use Fruchterman-Reingold layout for better community visualization
main = paste(plot_title, "(colored by community)"))
cat("Successfully plotted network with community colors\n")
# Add community legend if not too many communities
if (community_count > 1 && community_count <= 15) {  # Increased from 10 to 15
# Count nodes in each community for the legend
community_sizes <- table(comm_membership)
# Create legend text with community sizes
legend_text <- paste("Community", unique_communities,
"(", community_sizes[as.character(unique_communities)], "nodes)")
# Add the legend with community sizes
legend("topright",
legend = legend_text,
fill = community_colors,
cex = 0.6,
title = paste("Communities -", best_algorithm, "algorithm"))
} else if (community_count > 15) {
# For many communities, just show a summary
legend("topright",
legend = paste(community_count, "communities detected"),
cex = 0.8,
title = paste(best_algorithm, "algorithm"))
}
# Export community information to a separate file
if (community_count > 1) {
# Create a data frame with community information
community_data <- data.frame(
Node_ID = 1:vcount(network),
Group = rep(group_name, vcount(network)),
Community = comm_membership,
Degree = node_degrees,
Betweenness = node_betweenness,
OTU_ID = NA,
stringsAsFactors = FALSE
)
# Add OTU ID information if available
if (!is.null(taxonomy_data) && nrow(taxonomy_data) > 0) {
# Get the original indices (before filtering constant rows)
if (exists("filtered_indices") && length(filtered_indices) > 0) {
# Map network node IDs back to original OTU indices
original_indices <- filtered_indices[1:vcount(network)]
# Check if indices are within bounds
valid_indices <- original_indices <= nrow(taxonomy_data)
if (any(valid_indices)) {
# Add OTU ID (first column of taxonomy data)
for (i in 1:vcount(network)) {
if (valid_indices[i]) {
idx <- original_indices[i]
community_data$OTU_ID[i] <- as.character(taxonomy_data[idx, 1])
}
}
}
}
}
# Sort by community and then by degree (descending) within each community
community_data <- community_data[order(community_data$Community, -community_data$Degree), ]
# Save community information to a CSV file
community_file <- paste0("community_data_", tolower(group_name), ".csv")
write.csv(community_data, community_file, row.names = FALSE)
cat("Community information saved to", community_file, "\n")
}
}, error = function(e) {
cat("Note: Could not add community colors:", e$message, "\n")
})
# Add legend for edges and nodes
legend("bottomright",
legend = c("Positive correlation", "Negative correlation", "Hub node (high degree)", "Regular node"),
col = c("blue", "red", "black", "black"),
pch = c(NA, NA, 19, 19),
pt.cex = c(NA, NA, 2, 1),
lty = c(1, 1, NA, NA),
cex = 0.8)
# Close the PDF device
dev.off()
# Create a filtered visualization for bacteria networks (or other large networks)
if (group_name == "Bacteria" || vcount(network) > 1000) {
cat("\nCreating filtered visualization for", group_name, "network...\n")
# Create a filtered network based on node degree and edge weight
# For bacteria networks, we'll filter more aggressively
degree_threshold <- ifelse(group_name == "Bacteria", 10, 5)
weight_threshold <- ifelse(group_name == "Bacteria", 0.7, 0.65)
cat("Filtering nodes with degree <", degree_threshold, "and edges with weight <", weight_threshold, "\n")
# Create a copy of the network for filtering
filtered_network <- network
# First, identify nodes to keep (those with high degree)
high_degree_nodes <- which(node_degrees >= degree_threshold)
# If we have too few nodes after filtering, adjust the threshold
if (length(high_degree_nodes) < 50) {
# Find a threshold that keeps at least 50 nodes or 10% of the original nodes
min_nodes <- min(50, ceiling(vcount(network) * 0.1))
sorted_degrees <- sort(node_degrees, decreasing = TRUE)
if (length(sorted_degrees) >= min_nodes) {
degree_threshold <- sorted_degrees[min_nodes]
high_degree_nodes <- which(node_degrees >= degree_threshold)
cat("Adjusted degree threshold to", degree_threshold, "to keep at least", min_nodes, "nodes\n")
}
}
# Create a subgraph with only the high-degree nodes
if (length(high_degree_nodes) > 0) {
filtered_network <- induced_subgraph(network, high_degree_nodes)
# Further filter edges by weight
edges_to_remove <- which(E(filtered_network)$abs_weight < weight_threshold)
if (length(edges_to_remove) > 0) {
filtered_network <- delete_edges(filtered_network, edges_to_remove)
}
# Remove isolated nodes (degree 0 after edge filtering)
isolated_nodes <- which(degree(filtered_network) == 0)
if (length(isolated_nodes) > 0) {
filtered_network <- delete_vertices(filtered_network, isolated_nodes)
}
# Only proceed if we have nodes and edges left
if (vcount(filtered_network) > 0 && ecount(filtered_network) > 0) {
# Create a PDF for the filtered network
pdf(paste0("network_", tolower(group_name), "_filtered.pdf"))
# Recalculate node properties for the filtered network
filtered_node_degrees <- degree(filtered_network)
# Scale node sizes based on degree
min_size <- 3  # Slightly larger minimum size for better visibility
max_size <- 12
if (max(filtered_node_degrees) > min(filtered_node_degrees)) {
filtered_node_sizes <- min_size + (max_size - min_size) *
(filtered_node_degrees - min(filtered_node_degrees)) /
(max(filtered_node_degrees) - min(filtered_node_degrees))
} else {
filtered_node_sizes <- rep(min_size, length(filtered_node_degrees))
}
# Edge properties
filtered_edge_colors <- ifelse(E(filtered_network)$weight > 0, "blue", "red")
filtered_edge_widths <- abs(E(filtered_network)$weight) * 3  # Slightly thicker for better visibility
# Plot the filtered network
# Create a layout with longer edges to spread out nodes
# Use Fruchterman-Reingold layout with increased area parameter
fr_layout <- layout_with_fr(filtered_network,
area = vcount(filtered_network)^2.5, # Increase area to spread nodes
repulserad = vcount(filtered_network)^3, # Increase repulsion radius
coolexp = 1.5) # Slower cooling for better layout
# Plot with standard layout
plot(filtered_network,
vertex.size = filtered_node_sizes,
vertex.label = NA,
edge.color = filtered_edge_colors,
edge.width = filtered_edge_widths,
layout = layout_with_fr(filtered_network),
main = paste(plot_title, "(filtered view)"))
# Plot with expanded layout
plot(filtered_network,
vertex.size = filtered_node_sizes,
vertex.label = NA,
edge.color = filtered_edge_colors,
edge.width = filtered_edge_widths,
layout = fr_layout,
main = paste(plot_title, "(filtered view, expanded layout)"))
# If the filtered network has community structure, color by community
if ("community" %in% vertex_attr_names(filtered_network)) {
# Get community membership
filtered_comm_membership <- V(filtered_network)$community
# Get unique communities
unique_communities <- sort(unique(filtered_comm_membership))
community_count <- length(unique_communities)
# Generate colors for communities
if (community_count <= 20) {
community_colors <- c("#E41A1C", "#377EB8", "#4DAF4A", "#984EA3",
"#FF7F00", "#FFFF33", "#A65628", "#F781BF",
"#66C2A5", "#FC8D62", "#8DA0CB", "#E78AC3",
"#A6D854", "#FFD92F", "#E5C494", "#B3B3B3",
"#8DD3C7", "#BEBADA", "#FB8072", "#80B1D3")
community_colors <- community_colors[1:min(community_count, length(community_colors))]
} else {
community_colors <- colorRampPalette(c("#2166AC", "#67A9CF", "#D1E5F0",
"#F7F7F7", "#FDDBC7", "#EF8A62",
"#B2182B"))(community_count)
}
# Create a mapping from community IDs to color indices
color_mapping <- setNames(seq_along(unique_communities), unique_communities)
# Assign colors to vertices
V(filtered_network)$color <- community_colors[color_mapping[filtered_comm_membership]]
# Plot with community colors - standard layout
plot(filtered_network,
vertex.size = filtered_node_sizes,
vertex.label = NA,
vertex.color = V(filtered_network)$color,
edge.color = filtered_edge_colors,
edge.width = filtered_edge_widths,
layout = layout_with_fr(filtered_network),
main = paste(plot_title, "(filtered view, colored by community)"))
# Plot with community colors - expanded layout
plot(filtered_network,
vertex.size = filtered_node_sizes,
vertex.label = NA,
vertex.color = V(filtered_network)$color,
edge.color = filtered_edge_colors,
edge.width = filtered_edge_widths,
layout = fr_layout,
main = paste(plot_title, "(filtered view, colored by community, expanded layout)"))
# Add legend if not too many communities
if (community_count <= 15) {
# Count nodes in each community
community_sizes <- table(filtered_comm_membership)
# Create legend text
legend_text <- paste("Community", unique_communities,
"(", community_sizes[as.character(unique_communities)], "nodes)")
# Add legend
legend("topright",
legend = legend_text,
fill = community_colors[1:community_count],
cex = 0.6,
title = "Communities")
}
}
# Add legend for edges and nodes
legend("bottomright",
legend = c("Positive correlation", "Negative correlation", "Hub node", "Regular node"),
col = c("blue", "red", "black", "black"),
pch = c(NA, NA, 19, 19),
pt.cex = c(NA, NA, 2, 1),
lty = c(1, 1, NA, NA),
cex = 0.8)
# Close the PDF
dev.off()
cat("Filtered network visualization saved to network_", tolower(group_name), "_filtered.pdf\n", sep="")
# Save filtered network node information
filtered_nodes_file <- paste0("filtered_nodes_", tolower(group_name), ".csv")
filtered_nodes_data <- data.frame(
Node_ID = as.numeric(names(V(filtered_network))),
Degree = filtered_node_degrees,
Original_Node_ID = V(filtered_network)$name,
stringsAsFactors = FALSE
)
write.csv(filtered_nodes_data, filtered_nodes_file, row.names = FALSE)
cat("Filtered network node information saved to", filtered_nodes_file, "\n")
} else {
cat("After filtering, no nodes or edges remain. Skipping filtered visualization.\n")
}
} else {
cat("No nodes meet the filtering criteria. Skipping filtered visualization.\n")
}
}
# Return network analysis results
return(list(
network = network,
communities = communities,
degree = node_degrees,
betweenness = node_betweenness,
closeness = closeness(network, weights = abs(E(network)$weight)),
hub_nodes = hub_nodes,
node_sizes = node_sizes,
community_count = community_count,
modularity = mod_value
))
}
# Create filtered datasets for old and new samples
cat("\n========== FILTERING SAMPLES BY ENVIRONMENT ==========\n")
# Bacteria Alpine
bacteria_alpine_old <- filter_samples_by_environment(bacteria_alpine_otu, bacteria_alpine_meta, "old")
bacteria_alpine_new <- filter_samples_by_environment(bacteria_alpine_otu, bacteria_alpine_meta, "new")
# Bacteria Farming
bacteria_farming_old <- filter_samples_by_environment(bacteria_farming_otu, bacteria_farming_meta, "old")
bacteria_farming_new <- filter_samples_by_environment(bacteria_farming_otu, bacteria_farming_meta, "new")
# Fungi Alpine
fungi_alpine_old <- filter_samples_by_environment(fungi_alpine_otu, fungi_alpine_meta, "old")
fungi_alpine_new <- filter_samples_by_environment(fungi_alpine_otu, fungi_alpine_meta, "new")
