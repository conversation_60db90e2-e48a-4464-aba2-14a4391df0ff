# Test script to check taxonomic aggregation at different levels
library(readxl)
library(tidyverse)

# Read one bacteria and one fungi dataset to test aggregation
bacteria_alpine_otu <- read_excel("OTU_table_bacteria_alpine_samples.xlsx")
bacteria_alpine_tax <- read_excel("taxonomy_bacteria_alpine_samples.xlsx")
fungi_alpine_otu <- read_excel("OTU_table_fungi_alpine_samples.xlsx")
fungi_alpine_tax <- read_excel("taxonomy_fungi_alpine_samples.xlsx")

cat("Original data dimensions:\n")
cat("Bacteria Alpine OTU:", dim(bacteria_alpine_otu), "\n")
cat("Fungi Alpine OTU:", dim(fungi_alpine_otu), "\n")

# Function to aggregate OTUs to specified taxonomic level
aggregate_to_taxonomic_level <- function(otu_table, taxonomy_table, taxonomic_level) {
  cat("\n--- Aggregating to", taxonomic_level, "level ---\n")
  
  # Check if taxonomic level exists in taxonomy table
  if (!taxonomic_level %in% colnames(taxonomy_table)) {
    stop("Taxonomic level '", taxonomic_level, "' not found in taxonomy table")
  }
  
  # Get the first column name (OTU ID column)
  otu_id_col <- colnames(otu_table)[1]
  tax_otu_id_col <- colnames(taxonomy_table)[1]
  
  # Merge OTU table with taxonomy
  merged_data <- merge(otu_table, taxonomy_table[, c(tax_otu_id_col, taxonomic_level)], 
                       by.x = otu_id_col, by.y = tax_otu_id_col, all.x = TRUE)
  
  # Remove rows with NA or empty taxonomic assignment
  valid_rows <- !is.na(merged_data[[taxonomic_level]]) & 
                merged_data[[taxonomic_level]] != "" & 
                merged_data[[taxonomic_level]] != "NA"
  
  if (sum(valid_rows) == 0) {
    stop("No valid taxonomic assignments found at ", taxonomic_level, " level")
  }
  
  cat("Removed", sum(!valid_rows), "OTUs with missing", taxonomic_level, "assignment\n")
  merged_data <- merged_data[valid_rows, ]
  
  # Get sample columns (all columns except OTU ID and taxonomic level)
  sample_cols <- setdiff(colnames(merged_data), c(otu_id_col, taxonomic_level))
  
  # Aggregate by summing OTU counts within each taxonomic group
  aggregated <- merged_data %>%
    group_by(!!sym(taxonomic_level)) %>%
    summarise(across(all_of(sample_cols), sum, na.rm = TRUE), .groups = 'drop')
  
  # Rename the taxonomic level column to match original OTU ID column name
  colnames(aggregated)[1] <- otu_id_col
  
  cat("Aggregated from", nrow(otu_table), "OTUs to", nrow(aggregated), taxonomic_level, "groups\n")
  
  return(as.data.frame(aggregated))
}

# Test aggregation at different taxonomic levels
taxonomic_levels <- c("Genus", "Family", "Order", "Class", "Phylum")

cat("\n========== BACTERIA ALPINE AGGREGATION TEST ==========\n")
for (level in taxonomic_levels) {
  tryCatch({
    agg_result <- aggregate_to_taxonomic_level(bacteria_alpine_otu, bacteria_alpine_tax, level)
    cat("SUCCESS:", level, "level -", nrow(agg_result), "groups\n")
  }, error = function(e) {
    cat("ERROR:", level, "level -", e$message, "\n")
  })
}

cat("\n========== FUNGI ALPINE AGGREGATION TEST ==========\n")
for (level in taxonomic_levels) {
  tryCatch({
    agg_result <- aggregate_to_taxonomic_level(fungi_alpine_otu, fungi_alpine_tax, level)
    cat("SUCCESS:", level, "level -", nrow(agg_result), "groups\n")
  }, error = function(e) {
    cat("ERROR:", level, "level -", e$message, "\n")
  })
}

# Test detailed aggregation for Genus level
cat("\n========== DETAILED GENUS LEVEL TEST ==========\n")

# Bacteria
bacteria_genus <- aggregate_to_taxonomic_level(bacteria_alpine_otu, bacteria_alpine_tax, "Genus")
cat("Bacteria genera (first 10):", paste(bacteria_genus[[1]][1:min(10, nrow(bacteria_genus))], collapse = ", "), "\n")

# Fungi
fungi_genus <- aggregate_to_taxonomic_level(fungi_alpine_otu, fungi_alpine_tax, "Genus")
cat("Fungi genera (first 10):", paste(fungi_genus[[1]][1:min(10, nrow(fungi_genus))], collapse = ", "), "\n")

# Check for any overlapping names (shouldn't be any between bacteria and fungi)
overlap <- intersect(bacteria_genus[[1]], fungi_genus[[1]])
if (length(overlap) > 0) {
  cat("WARNING: Overlapping taxonomic names found:", paste(overlap, collapse = ", "), "\n")
} else {
  cat("Good: No overlapping taxonomic names between bacteria and fungi\n")
}

cat("\nTaxonomic aggregation test completed!\n")
