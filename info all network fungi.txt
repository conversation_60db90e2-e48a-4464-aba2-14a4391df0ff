> # Fungi Alpine Old (fungi datasets are smaller, can handle more OTUs)
> if (!is.null(fungi_alpine_old)) {
+   fungi_alpine_old_cor <- create_correlation_matrix(fungi_alpine_old, threshold = 0.6, max_otus = 6000)
+   fungi_alpine_old_network <- analyze_network(fungi_alpine_old_cor, fungi_alpine_tax, "Fungi_Alpine_Old")
+ }

OTU table dimensions: 6269 33 
Warning: Removing 5006 constant rows (all zeros or all same value)
Estimated memory requirement: 0.01 GB
Calculating correlation matrix...

Before thresholding:
Total correlations: 1595169 
Positive correlations: 427317 
Negative correlations: 1166720 
Range: -0.743104 1 

After thresholding (|correlation| ≥ 0.6 ):
Total non-zero correlations: 40268 
Positive correlations: 40268 
Negative correlations: 0 
Range of non-zero correlations: 0.6000086 1 

Summary of correlations:
Total correlations: 40268 
Positive correlations: 40268 
Negative correlations: 0 
Range: 0.6000086 1 

Removing duplicate edges...
Successfully simplified the network
Detecting communities using multiple algorithms...
Trying Fast Greedy algorithm...
Error in Fast Greedy algorithm: At vendor/cigraph/src/community/fast_modularity.c:668 : Fast greedy community detection works only on graphs without multi-edges. Invalid value 
Trying Louvain algorithm...
Louvain modularity: 0.7693659 
Trying Walktrap algorithm...
Walktrap modularity: 0.7581526 
Trying Label Propagation algorithm...
Label Propagation modularity: 0.7071818 
Trying Infomap algorithm...
Infomap modularity: -0.00117691 
Selected Louvain algorithm with modularity 0.7693659 

Network Analysis Results for Fungi_Alpine_Old 
Number of nodes: 1263 
Number of edges: 40268 
Positive edges: 40268 
Negative edges: 0 
Network density: 0.05052745 
Number of communities: 38 
Modularity: 0.7693659 
Selected 1142 hub nodes with degree > 6
Hub node information saved to hub_nodes_fungi_alpine_old.csv 
Found 38 communities
Successfully plotted network with community colors
Community information saved to community_data_fungi_alpine_old.csv 

Creating filtered visualization for Fungi_Alpine_Old network...
Filtering nodes with degree < 5 and edges with weight < 0.65 
Filtered network visualization saved to network_fungi_alpine_old_filtered.pdf
Filtered network node information saved to filtered_nodes_fungi_alpine_old.csv 
Messaggi di avvertimento:
1: In layout_with_fr(filtered_network, area = vcount(filtered_network)^2.5,  :
  Argument `coolexp' is deprecated and has no effect
2: In layout_with_fr(filtered_network, area = vcount(filtered_network)^2.5,  :
  Argument `area' is deprecated and has no effect
3: In layout_with_fr(filtered_network, area = vcount(filtered_network)^2.5,  :
  Argument `repulserad' is deprecated and has no effect
> # Fungi Alpine New
> if (!is.null(fungi_alpine_new)) {
+   fungi_alpine_new_cor <- create_correlation_matrix(fungi_alpine_new, threshold = 0.6, max_otus = 6000)
+   fungi_alpine_new_network <- analyze_network(fungi_alpine_new_cor, fungi_alpine_tax, "Fungi_Alpine_New")
+ }

OTU table dimensions: 6269 57 
Warning: Removing 365 constant rows (all zeros or all same value)
Estimated memory requirement: 0.26 GB
Calculating correlation matrix...

Before thresholding:
Total correlations: 34857216 
Positive correlations: 9133910 
Negative correlations: 25705516 
Range: -0.8536098 1 

After thresholding (|correlation| ≥ 0.6 ):
Total non-zero correlations: 271308 
Positive correlations: 271308 
Negative correlations: 0 
Range of non-zero correlations: 0.6000062 1 

Summary of correlations:
Total correlations: 271308 
Positive correlations: 271308 
Negative correlations: 0 
Range: 0.6000062 1 

Removing duplicate edges...
Successfully simplified the network
Detecting communities using multiple algorithms...
Trying Louvain algorithm...
Louvain modularity: 0.7702947 
Trying Walktrap algorithm...
Walktrap modularity: 0.7139161 
Trying Label Propagation algorithm...
Label Propagation modularity: 0.7427124 
Selected Louvain algorithm with modularity 0.7702947 

Network Analysis Results for Fungi_Alpine_New 
Number of nodes: 5904 
Number of edges: 271308 
Positive edges: 271308 
Negative edges: 0 
Network density: 0.01556946 
Number of communities: 515 
Modularity: 0.7702947 
Selected 4720 hub nodes with degree > 6
Hub node information saved to hub_nodes_fungi_alpine_new.csv 
Found 515 communities
Successfully plotted network with community colors
Community information saved to community_data_fungi_alpine_new.csv 

Creating filtered visualization for Fungi_Alpine_New network...
Filtering nodes with degree < 5 and edges with weight < 0.65 
Filtered network visualization saved to network_fungi_alpine_new_filtered.pdf
Filtered network node information saved to filtered_nodes_fungi_alpine_new.csv 
Messaggi di avvertimento:
1: In layout_with_fr(filtered_network, area = vcount(filtered_network)^2.5,  :
  Argument `coolexp' is deprecated and has no effect
2: In layout_with_fr(filtered_network, area = vcount(filtered_network)^2.5,  :
  Argument `area' is deprecated and has no effect
3: In layout_with_fr(filtered_network, area = vcount(filtered_network)^2.5,  :
  Argument `repulserad' is deprecated and has no effect
> # Fungi Farming Old
> if (!is.null(fungi_farming_old)) {
+   fungi_farming_old_cor <- create_correlation_matrix(fungi_farming_old, threshold = 0.6, max_otus = 6000)
+   fungi_farming_old_network <- analyze_network(fungi_farming_old_cor, fungi_farming_tax, "Fungi_Farming_Old")
+ }

OTU table dimensions: 7070 64 
Warning: Removing 1517 constant rows (all zeros or all same value)
Estimated memory requirement: 0.23 GB
Calculating correlation matrix...

Before thresholding:
Total correlations: 30835809 
Positive correlations: 8132271 
Negative correlations: 22681838 
Range: -0.5886325 1 

After thresholding (|correlation| ≥ 0.6 ):
Total non-zero correlations: 2176166 
Positive correlations: 2176166 
Negative correlations: 0 
Range of non-zero correlations: 0.600024 1 

Summary of correlations:
Total correlations: 2176166 
Positive correlations: 2176166 
Negative correlations: 0 
Range: 0.600024 1 

Removing duplicate edges...
Successfully simplified the network
Detecting communities using multiple algorithms...
Trying Louvain algorithm...
Louvain modularity: 0.1362639 
Trying Walktrap algorithm...
Walktrap modularity: 0.07497331 
Trying Label Propagation algorithm...
Label Propagation modularity: 0.08659908 
Selected Louvain algorithm with modularity 0.1362639 

Network Analysis Results for Fungi_Farming_Old 
Number of nodes: 5553 
Number of edges: 2176166 
Positive edges: 2176166 
Negative edges: 0 
Network density: 0.1411708 
Number of communities: 704 
Modularity: 0.1362639 
Selected 4225 hub nodes with degree > 6
Hub node information saved to hub_nodes_fungi_farming_old.csv 
Found 704 communities
Successfully plotted network with community colors
Community information saved to community_data_fungi_farming_old.csv 

Creating filtered visualization for Fungi_Farming_Old network...
Filtering nodes with degree < 5 and edges with weight < 0.65 
Filtered network visualization saved to network_fungi_farming_old_filtered.pdf
Filtered network node information saved to filtered_nodes_fungi_farming_old.csv 
Messaggi di avvertimento:
1: In layout_with_fr(filtered_network, area = vcount(filtered_network)^2.5,  :
  Argument `coolexp' is deprecated and has no effect
2: In layout_with_fr(filtered_network, area = vcount(filtered_network)^2.5,  :
  Argument `area' is deprecated and has no effect
3: In layout_with_fr(filtered_network, area = vcount(filtered_network)^2.5,  :
  Argument `repulserad' is deprecated and has no effect
> # Fungi Farming New
> if (!is.null(fungi_farming_new)) {
+   fungi_farming_new_cor <- create_correlation_matrix(fungi_farming_new, threshold = 0.6, max_otus = 6000)
+   fungi_farming_new_network <- analyze_network(fungi_farming_new_cor, fungi_farming_tax, "Fungi_Farming_New")
+ }

OTU table dimensions: 7070 65 
Warning: Removing 2993 constant rows (all zeros or all same value)
Estimated memory requirement: 0.12 GB
Calculating correlation matrix...

Before thresholding:
Total correlations: 16621929 
Positive correlations: 4494559 
Negative correlations: 12122100 
Range: -0.5883202 1 

After thresholding (|correlation| ≥ 0.6 ):
Total non-zero correlations: 128908 
Positive correlations: 128908 
Negative correlations: 0 
Range of non-zero correlations: 0.600001 1 

Summary of correlations:
Total correlations: 128908 
Positive correlations: 128908 
Negative correlations: 0 
Range: 0.600001 1 

Removing duplicate edges...
Successfully simplified the network
Detecting communities using multiple algorithms...
Trying Fast Greedy algorithm...
Error in Fast Greedy algorithm: At vendor/cigraph/src/community/fast_modularity.c:668 : Fast greedy community detection works only on graphs without multi-edges. Invalid value 
Trying Louvain algorithm...
Louvain modularity: 0.7545967 
Trying Walktrap algorithm...
Walktrap modularity: 0.7137915 
Trying Label Propagation algorithm...
Label Propagation modularity: 0.7403182 
Selected Louvain algorithm with modularity 0.7545967 

Network Analysis Results for Fungi_Farming_New 
Number of nodes: 4077 
Number of edges: 128908 
Positive edges: 128908 
Negative edges: 0 
Network density: 0.0155144 
Number of communities: 625 
Modularity: 0.7545967 
Selected 2947 hub nodes with degree > 6
Hub node information saved to hub_nodes_fungi_farming_new.csv 
Found 625 communities
Successfully plotted network with community colors
Community information saved to community_data_fungi_farming_new.csv 

Creating filtered visualization for Fungi_Farming_New network...
Filtering nodes with degree < 5 and edges with weight < 0.65 
Filtered network visualization saved to network_fungi_farming_new_filtered.pdf
Filtered network node information saved to filtered_nodes_fungi_farming_new.csv 
Messaggi di avvertimento:
1: In layout_with_fr(filtered_network, area = vcount(filtered_network)^2.5,  :
  Argument `coolexp' is deprecated and has no effect
2: In layout_with_fr(filtered_network, area = vcount(filtered_network)^2.5,  :
  Argument `area' is deprecated and has no effect
3: In layout_with_fr(filtered_network, area = vcount(filtered_network)^2.5,  :
  Argument `repulserad' is deprecated and has no effect