# Test the plotting functions
library(readxl)
library(igraph)
library(vegan)
library(tidyverse)

# Configuration
TAXONOMIC_LEVEL <- "Family"

# Read and process one dataset quickly for testing
bacteria_alpine_otu <- read_excel("OTU_table_bacteria_alpine_samples.xlsx")
bacteria_alpine_tax <- read_excel("taxonomy_bacteria_alpine_samples.xlsx")
bacteria_alpine_meta <- read_excel("metadata_bacteria_alpine_samples.xlsx")

# Quick aggregation function
aggregate_to_taxonomic_level <- function(otu_table, taxonomy_table, taxonomic_level) {
  otu_id_col <- colnames(otu_table)[1]
  tax_otu_id_col <- colnames(taxonomy_table)[1]
  
  merged_data <- merge(otu_table, taxonomy_table[, c(tax_otu_id_col, taxonomic_level)], 
                       by.x = otu_id_col, by.y = tax_otu_id_col, all.x = TRUE)
  
  valid_rows <- !is.na(merged_data[[taxonomic_level]]) & 
                merged_data[[taxonomic_level]] != "" & 
                merged_data[[taxonomic_level]] != "NA"
  
  merged_data <- merged_data[valid_rows, ]
  sample_cols <- setdiff(colnames(merged_data), c(otu_id_col, taxonomic_level))
  
  aggregated <- merged_data %>%
    group_by(!!sym(taxonomic_level)) %>%
    summarise(across(all_of(sample_cols), \(x) sum(x, na.rm = TRUE)), .groups = 'drop')
  
  colnames(aggregated)[1] <- otu_id_col
  return(as.data.frame(aggregated))
}

# Quick filter function
filter_samples_by_environment <- function(otu_table, metadata, environment_type) {
  sample_id_col <- if ("Sample_ID" %in% colnames(metadata)) "Sample_ID" else "sample"
  target_samples <- metadata[[sample_id_col]][grepl(environment_type, metadata$Environment, ignore.case = TRUE)]
  sample_cols <- which(colnames(otu_table) %in% target_samples)
  if (length(sample_cols) == 0) return(NULL)
  return(otu_table[, c(1, sample_cols)])
}

# Quick correlation function
create_taxonomic_correlation_matrix <- function(taxonomic_table, threshold = 0.6) {
  abundance_matrix <- as.matrix(taxonomic_table[,-1])
  rownames(abundance_matrix) <- taxonomic_table[[1]]
  
  row_sums <- rowSums(abundance_matrix)
  constant_rows <- row_sums == 0 | apply(abundance_matrix, 1, function(x) length(unique(x)) <= 1)
  if (any(constant_rows)) {
    abundance_matrix <- abundance_matrix[!constant_rows, , drop = FALSE]
  }
  
  cor_matrix <- cor(t(abundance_matrix), method = "spearman", use = "pairwise.complete.obs")
  if (any(is.na(cor_matrix))) cor_matrix[is.na(cor_matrix)] <- 0
  
  cor_matrix[abs(cor_matrix) < threshold] <- 0
  diag(cor_matrix) <- 0
  
  return(list(
    cor_matrix = cor_matrix,
    taxa_names = rownames(abundance_matrix),
    n_taxa = nrow(abundance_matrix)
  ))
}

# Quick network analysis function
analyze_taxonomic_network <- function(cor_result, group_name) {
  cor_matrix <- cor_result$cor_matrix
  taxa_names <- cor_result$taxa_names
  
  if (sum(cor_matrix != 0) == 0) return(NULL)
  
  network <- graph_from_adjacency_matrix(abs(cor_matrix), mode = "undirected", weighted = TRUE, diag = FALSE)
  V(network)$name <- taxa_names
  
  edge_list <- as_edgelist(network, names = FALSE)
  edge_correlations <- numeric(nrow(edge_list))
  edge_signs <- character(nrow(edge_list))
  
  for (i in 1:nrow(edge_list)) {
    row_idx <- edge_list[i, 1]
    col_idx <- edge_list[i, 2]
    correlation_value <- cor_matrix[row_idx, col_idx]
    edge_correlations[i] <- correlation_value
    edge_signs[i] <- ifelse(correlation_value > 0, "positive", "negative")
  }
  
  E(network)$sign <- edge_signs
  E(network)$correlation <- edge_correlations
  
  node_degrees <- degree(network)
  betweenness <- betweenness(network)
  closeness <- closeness(network)
  communities <- cluster_louvain(network)
  community_count <- length(communities)
  modularity_score <- modularity(communities)
  degree_threshold <- quantile(node_degrees, 0.9)
  hub_nodes <- names(node_degrees[node_degrees >= degree_threshold])
  
  return(list(
    network = network,
    node_degrees = node_degrees,
    betweenness = betweenness,
    closeness = closeness,
    communities = communities,
    community_count = community_count,
    modularity = modularity_score,
    hub_nodes = hub_nodes,
    taxa_names = taxa_names,
    group_name = group_name
  ))
}

# Function to plot individual networks
plot_individual_network <- function(network_result, group_name, taxonomic_level) {
  if (is.null(network_result)) return(NULL)
  
  tryCatch({
    network <- network_result$network
    layout <- layout_with_fr(network)
    
    community_colors <- rainbow(network_result$community_count)
    node_colors <- community_colors[membership(network_result$communities)]
    node_sizes <- (network_result$node_degrees / max(network_result$node_degrees)) * 15 + 5
    edge_colors <- ifelse(E(network)$sign == "positive", "blue", "red")
    
    filename <- paste0(group_name, "_", taxonomic_level, "_network.pdf")
    
    pdf(filename, width = 12, height = 10)
    
    plot(network,
         layout = layout,
         vertex.color = node_colors,
         vertex.size = node_sizes,
         vertex.label = ifelse(network_result$node_degrees >= quantile(network_result$node_degrees, 0.9), 
                              V(network)$name, ""),
         vertex.label.cex = 0.7,
         vertex.label.color = "black",
         edge.color = edge_colors,
         edge.width = abs(E(network)$correlation) * 2,
         main = paste(group_name, "Network at", taxonomic_level, "Level"),
         sub = paste("Nodes:", vcount(network), "| Edges:", ecount(network), 
                    "| Communities:", network_result$community_count))
    
    legend("topright", 
           legend = c("Positive correlation", "Negative correlation", "Hub nodes"),
           col = c("blue", "red", "black"),
           lty = c(1, 1, NA),
           pch = c(NA, NA, 16),
           cex = 0.8)
    
    dev.off()
    cat("Network plot saved:", filename, "\n")
    
  }, error = function(e) {
    cat("Error creating network plot:", e$message, "\n")
  })
}

# Test the complete workflow with plotting
cat("========== TESTING PLOTTING WORKFLOW ==========\n")

# Process data
bacteria_alpine_agg <- aggregate_to_taxonomic_level(bacteria_alpine_otu, bacteria_alpine_tax, TAXONOMIC_LEVEL)
bacteria_alpine_old <- filter_samples_by_environment(bacteria_alpine_agg, bacteria_alpine_meta, "old")

if (!is.null(bacteria_alpine_old)) {
  bacteria_alpine_old_cor <- create_taxonomic_correlation_matrix(bacteria_alpine_old, threshold = 0.6)
  
  if (!is.null(bacteria_alpine_old_cor)) {
    bacteria_alpine_old_network <- analyze_taxonomic_network(bacteria_alpine_old_cor, "Bacteria_Alpine_Old")
    
    if (!is.null(bacteria_alpine_old_network)) {
      # Test plotting
      plot_individual_network(bacteria_alpine_old_network, "TEST_Bacteria_Alpine_Old", TAXONOMIC_LEVEL)
      
      cat("\n🎉 PLOTTING TEST SUCCESS! 🎉\n")
      cat("Network plot should be generated as: TEST_Bacteria_Alpine_Old_Family_network.pdf\n")
    }
  }
}

cat("\nPlotting test completed!\n")
