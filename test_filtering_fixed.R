# Test script to verify the updated filtering function works
library(readxl)
library(tidyverse)

# Function to filter samples by environment (old vs new)
filter_samples_by_environment <- function(otu_table, metadata, environment_type) {
  # Determine the sample ID column name (different files use different names)
  sample_id_col <- NULL
  if ("Sample_ID" %in% colnames(metadata)) {
    sample_id_col <- "Sample_ID"
  } else if ("sample" %in% colnames(metadata)) {
    sample_id_col <- "sample"
  } else {
    cat("Error: Could not find sample ID column in metadata\n")
    cat("Available columns:", paste(colnames(metadata), collapse = ", "), "\n")
    return(NULL)
  }
  
  # Get sample IDs for the specified environment
  target_samples <- metadata[[sample_id_col]][grepl(environment_type, metadata$Environment, ignore.case = TRUE)]
  
  cat("Using sample ID column:", sample_id_col, "\n")
  cat("Found", length(target_samples), "target samples for environment", environment_type, "\n")
  
  # Find which columns in OTU table correspond to these samples
  # First column is typically OTU IDs, so we check from column 2 onwards
  sample_cols <- which(colnames(otu_table) %in% target_samples)
  
  if (length(sample_cols) == 0) {
    cat("Warning: No samples found for environment", environment_type, "\n")
    cat("Target samples:", paste(head(target_samples), collapse = ", "), "\n")
    cat("OTU table columns (first 10):", paste(colnames(otu_table)[1:min(10, ncol(otu_table))], collapse = ", "), "\n")
    return(NULL)
  }
  
  # Return OTU table with only the target samples (plus the first column with OTU IDs)
  filtered_table <- otu_table[, c(1, sample_cols)]
  cat("Filtered", environment_type, "samples:", ncol(filtered_table) - 1, "samples\n")
  return(filtered_table)
}

# Test with bacteria alpine (uses Sample_ID)
cat("=== Testing Bacteria Alpine ===\n")
bacteria_alpine_otu <- read_excel("OTU_table_bacteria_alpine_samples.xlsx")
bacteria_alpine_meta <- read_excel("metadata_bacteria_alpine_samples.xlsx")

cat("Bacteria Alpine metadata columns:", paste(colnames(bacteria_alpine_meta), collapse = ", "), "\n")
bacteria_alpine_old <- filter_samples_by_environment(bacteria_alpine_otu, bacteria_alpine_meta, "old")

# Test with fungi alpine (uses sample)
cat("\n=== Testing Fungi Alpine ===\n")
fungi_alpine_otu <- read_excel("OTU_table_fungi_alpine_samples.xlsx")
fungi_alpine_meta <- read_excel("metadata_fungi_alpine_samples.xlsx")

cat("Fungi Alpine metadata columns:", paste(colnames(fungi_alpine_meta), collapse = ", "), "\n")
fungi_alpine_old <- filter_samples_by_environment(fungi_alpine_otu, fungi_alpine_meta, "old")

if (!is.null(bacteria_alpine_old)) {
  cat("\nBacteria Alpine Old dimensions:", dim(bacteria_alpine_old), "\n")
}

if (!is.null(fungi_alpine_old)) {
  cat("Fungi Alpine Old dimensions:", dim(fungi_alpine_old), "\n")
}
