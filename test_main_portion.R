# Test a portion of the main script to verify it works
library(readxl)
library(igraph)
library(vegan)
library(tidyverse)

# Read data files for the new experiment with four datasets
# Bacteria datasets
bacteria_alpine_otu <- read_excel("OTU_table_bacteria_alpine_samples.xlsx")
bacteria_farming_otu <- read_excel("OTU_table_bacteria_farming_samples.xlsx")

# Fungi datasets  
fungi_alpine_otu <- read_excel("OTU_table_fungi_alpine_samples.xlsx")
fungi_farming_otu <- read_excel("OTU_table_fungi_farming_samples.xlsx")

# Taxonomy files
bacteria_alpine_tax <- read_excel("taxonomy_bacteria_alpine_samples.xlsx")
bacteria_farming_tax <- read_excel("taxonomy_bacteria_farming_samples.xlsx")
fungi_alpine_tax <- read_excel("taxonomy_fungi_alpine_samples.xlsx")
fungi_farming_tax <- read_excel("taxonomy_fungi_farming_samples.xlsx")

# Metadata files
bacteria_alpine_meta <- read_excel("metadata_bacteria_alpine_samples.xlsx")
bacteria_farming_meta <- read_excel("metadata_bacteria_farming_samples.xlsx")
fungi_alpine_meta <- read_excel("metadata_fungi_alpine_samples.xlsx")
fungi_farming_meta <- read_excel("metadata_fungi_farming_samples.xlsx")

# Print data dimensions for debugging
cat("Bacteria Alpine OTU dimensions:", dim(bacteria_alpine_otu), "\n")
cat("Bacteria Farming OTU dimensions:", dim(bacteria_farming_otu), "\n")
cat("Fungi Alpine OTU dimensions:", dim(fungi_alpine_otu), "\n")
cat("Fungi Farming OTU dimensions:", dim(fungi_farming_otu), "\n")

# Function to filter samples by environment (old vs new)
filter_samples_by_environment <- function(otu_table, metadata, environment_type) {
  # Determine the sample ID column name (different files use different names)
  sample_id_col <- NULL
  if ("Sample_ID" %in% colnames(metadata)) {
    sample_id_col <- "Sample_ID"
  } else if ("sample" %in% colnames(metadata)) {
    sample_id_col <- "sample"
  } else {
    cat("Error: Could not find sample ID column in metadata\n")
    cat("Available columns:", paste(colnames(metadata), collapse = ", "), "\n")
    return(NULL)
  }
  
  # Get sample IDs for the specified environment
  target_samples <- metadata[[sample_id_col]][grepl(environment_type, metadata$Environment, ignore.case = TRUE)]
  
  cat("Using sample ID column:", sample_id_col, "\n")
  cat("Found", length(target_samples), "target samples for environment", environment_type, "\n")
  
  # Find which columns in OTU table correspond to these samples
  # First column is typically OTU IDs, so we check from column 2 onwards
  sample_cols <- which(colnames(otu_table) %in% target_samples)
  
  if (length(sample_cols) == 0) {
    cat("Warning: No samples found for environment", environment_type, "\n")
    cat("Target samples:", paste(head(target_samples), collapse = ", "), "\n")
    cat("OTU table columns (first 10):", paste(colnames(otu_table)[1:min(10, ncol(otu_table))], collapse = ", "), "\n")
    return(NULL)
  }
  
  # Return OTU table with only the target samples (plus the first column with OTU IDs)
  filtered_table <- otu_table[, c(1, sample_cols)]
  cat("Filtered", environment_type, "samples:", ncol(filtered_table) - 1, "samples\n")
  return(filtered_table)
}

# Create filtered datasets for old and new samples
cat("\n========== FILTERING SAMPLES BY ENVIRONMENT ==========\n")

# Bacteria Alpine
bacteria_alpine_old <- filter_samples_by_environment(bacteria_alpine_otu, bacteria_alpine_meta, "old")
bacteria_alpine_new <- filter_samples_by_environment(bacteria_alpine_otu, bacteria_alpine_meta, "new")

# Bacteria Farming  
bacteria_farming_old <- filter_samples_by_environment(bacteria_farming_otu, bacteria_farming_meta, "old")
bacteria_farming_new <- filter_samples_by_environment(bacteria_farming_otu, bacteria_farming_meta, "new")

# Fungi Alpine
fungi_alpine_old <- filter_samples_by_environment(fungi_alpine_otu, fungi_alpine_meta, "old")
fungi_alpine_new <- filter_samples_by_environment(fungi_alpine_otu, fungi_alpine_meta, "new")

# Fungi Farming
fungi_farming_old <- filter_samples_by_environment(fungi_farming_otu, fungi_farming_meta, "old")
fungi_farming_new <- filter_samples_by_environment(fungi_farming_otu, fungi_farming_meta, "new")

cat("\n========== SUMMARY OF FILTERED DATASETS ==========\n")
if (!is.null(bacteria_alpine_old)) cat("Bacteria Alpine Old:", dim(bacteria_alpine_old), "\n")
if (!is.null(bacteria_alpine_new)) cat("Bacteria Alpine New:", dim(bacteria_alpine_new), "\n")
if (!is.null(bacteria_farming_old)) cat("Bacteria Farming Old:", dim(bacteria_farming_old), "\n")
if (!is.null(bacteria_farming_new)) cat("Bacteria Farming New:", dim(bacteria_farming_new), "\n")
if (!is.null(fungi_alpine_old)) cat("Fungi Alpine Old:", dim(fungi_alpine_old), "\n")
if (!is.null(fungi_alpine_new)) cat("Fungi Alpine New:", dim(fungi_alpine_new), "\n")
if (!is.null(fungi_farming_old)) cat("Fungi Farming Old:", dim(fungi_farming_old), "\n")
if (!is.null(fungi_farming_new)) cat("Fungi Farming New:", dim(fungi_farming_new), "\n")

cat("\nData loading and filtering completed successfully!\n")
