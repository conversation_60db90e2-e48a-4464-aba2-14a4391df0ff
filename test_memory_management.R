# Test script to verify memory management works
library(readxl)
library(igraph)
library(vegan)
library(tidyverse)

# Read one of the large bacteria datasets
bacteria_alpine_otu <- read_excel("OTU_table_bacteria_alpine_samples.xlsx")
bacteria_alpine_meta <- read_excel("metadata_bacteria_alpine_samples.xlsx")

cat("Original bacteria alpine OTU dimensions:", dim(bacteria_alpine_otu), "\n")

# Function to filter samples by environment (old vs new)
filter_samples_by_environment <- function(otu_table, metadata, environment_type) {
  # Determine the sample ID column name (different files use different names)
  sample_id_col <- NULL
  if ("Sample_ID" %in% colnames(metadata)) {
    sample_id_col <- "Sample_ID"
  } else if ("sample" %in% colnames(metadata)) {
    sample_id_col <- "sample"
  } else {
    cat("Error: Could not find sample ID column in metadata\n")
    cat("Available columns:", paste(colnames(metadata), collapse = ", "), "\n")
    return(NULL)
  }
  
  # Get sample IDs for the specified environment
  target_samples <- metadata[[sample_id_col]][grepl(environment_type, metadata$Environment, ignore.case = TRUE)]
  
  cat("Using sample ID column:", sample_id_col, "\n")
  cat("Found", length(target_samples), "target samples for environment", environment_type, "\n")
  
  # Find which columns in OTU table correspond to these samples
  # First column is typically OTU IDs, so we check from column 2 onwards
  sample_cols <- which(colnames(otu_table) %in% target_samples)
  
  if (length(sample_cols) == 0) {
    cat("Warning: No samples found for environment", environment_type, "\n")
    return(NULL)
  }
  
  # Return OTU table with only the target samples (plus the first column with OTU IDs)
  filtered_table <- otu_table[, c(1, sample_cols)]
  cat("Filtered", environment_type, "samples:", ncol(filtered_table) - 1, "samples\n")
  return(filtered_table)
}

# Function to create correlation matrix with debugging and memory management
create_correlation_matrix <- function(otu_table, threshold = 0.6, max_otus = 5000) {
  # Print dimensions of input table
  cat("\nOTU table dimensions:", dim(otu_table), "\n")

  # Check if the table has at least one column besides the OTU IDs
  if (ncol(otu_table) <= 1) {
    stop("OTU table must have at least one sample column besides the OTU IDs")
  }

  # Convert to matrix and calculate correlations
  tryCatch({
    # Extract OTU matrix (remove first column which is likely OTU IDs)
    otu_matrix <- as.matrix(otu_table[,-1])

    # Check for rows with all zeros or constant values
    row_sums <- rowSums(otu_matrix)
    constant_rows <- row_sums == 0 | apply(otu_matrix, 1, function(x) length(unique(x)) <= 1)
    if (any(constant_rows)) {
      cat("Warning: Removing", sum(constant_rows), "constant rows (all zeros or all same value)\n")
      otu_matrix <- otu_matrix[!constant_rows, , drop = FALSE]
    }

    # Check if we still have enough rows
    if (nrow(otu_matrix) <= 1) {
      stop("Not enough variable rows for correlation analysis after removing constant rows")
    }

    # Memory management: If dataset is too large, filter to most abundant/variable OTUs
    if (nrow(otu_matrix) > max_otus) {
      cat("Dataset too large (", nrow(otu_matrix), " OTUs). Filtering to top", max_otus, "most abundant OTUs\n")
      
      # Calculate abundance and variance for each OTU
      otu_abundance <- rowMeans(otu_matrix)
      otu_variance <- apply(otu_matrix, 1, var)
      
      # Create a combined score (abundance + variance, both normalized)
      abundance_norm <- (otu_abundance - min(otu_abundance)) / (max(otu_abundance) - min(otu_abundance))
      variance_norm <- (otu_variance - min(otu_variance)) / (max(otu_variance) - min(otu_variance))
      combined_score <- abundance_norm + variance_norm
      
      # Select top OTUs based on combined score
      top_otus <- order(combined_score, decreasing = TRUE)[1:max_otus]
      otu_matrix <- otu_matrix[top_otus, , drop = FALSE]
      
      cat("Filtered to", nrow(otu_matrix), "OTUs based on abundance and variance\n")
    }

    # Estimate memory requirement
    n_otus <- nrow(otu_matrix)
    memory_gb <- (n_otus^2 * 8) / (1024^3)  # 8 bytes per double, convert to GB
    cat("Estimated memory requirement:", round(memory_gb, 2), "GB\n")
    
    if (memory_gb > 8) {
      cat("Warning: Large memory requirement. Consider reducing max_otus parameter.\n")
    }

    # Calculate correlations with pairwise.complete.obs to handle NAs
    cat("Calculating correlation matrix...\n")
    cor_matrix <- cor(t(otu_matrix), method = "spearman", use = "pairwise.complete.obs")
    
    cat("Correlation matrix calculated successfully!\n")
    cat("Correlation matrix dimensions:", dim(cor_matrix), "\n")
    
    return(list(
      cor_matrix = cor_matrix,
      n_original_otus = nrow(otu_table),
      n_filtered_otus = nrow(otu_matrix)
    ))
  }, error = function(e) {
    cat("Error in correlation calculation:", e$message, "\n")
    return(NULL)
  })
}

# Test filtering and correlation calculation
bacteria_alpine_old <- filter_samples_by_environment(bacteria_alpine_otu, bacteria_alpine_meta, "old")

if (!is.null(bacteria_alpine_old)) {
  cat("\n=== Testing correlation matrix creation ===\n")
  # Test with a small max_otus first
  result <- create_correlation_matrix(bacteria_alpine_old, threshold = 0.6, max_otus = 1000)
  
  if (!is.null(result)) {
    cat("SUCCESS: Correlation matrix created with", result$n_filtered_otus, "OTUs\n")
    cat("Original OTUs:", result$n_original_otus, "\n")
    cat("Filtered OTUs:", result$n_filtered_otus, "\n")
  } else {
    cat("FAILED: Could not create correlation matrix\n")
  }
}
