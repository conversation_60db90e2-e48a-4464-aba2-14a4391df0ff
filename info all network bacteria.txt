========== INDIVIDUAL NETWORK ANALYSES ==========
> # Bacteria Alpine Old (use smaller max_otus for large datasets)
> if (!is.null(bacteria_alpine_old)) {
+   bacteria_alpine_old_cor <- create_correlation_matrix(bacteria_alpine_old, threshold = 0.6, max_otus = 3000)
+   bacteria_alpine_old_network <- analyze_network(bacteria_alpine_old_cor, bacteria_alpine_tax, "Bacteria_Alpine_Old")
+ }

OTU table dimensions: 79887 37 
Warning: Removing 27912 constant rows (all zeros or all same value)
Dataset too large ( 51975  OTUs). Filtering to top 3000 most abundant OTUs
Filtered to 3000 OTUs based on abundance and variance
Estimated memory requirement: 0.07 GB
Calculating correlation matrix...

Before thresholding:
Total correlations: 9000000 
Positive correlations: 4713598 
Negative correlations: 4285280 
Range: -0.9015788 1 

After thresholding (|correlation| ≥ 0.6 ):
Total non-zero correlations: 314698 
Positive correlations: 314698 
Negative correlations: 0 
Range of non-zero correlations: 0.6 1 

Summary of correlations:
Total correlations: 314698 
Positive correlations: 314698 
Negative correlations: 0 
Range: 0.6 1 

Removing duplicate edges...
Successfully simplified the network
Detecting communities using multiple algorithms...
Trying Fast Greedy algorithm...
Error in Fast Greedy algorithm: At vendor/cigraph/src/community/fast_modularity.c:668 : Fast greedy community detection works only on graphs without multi-edges. Invalid value 
Trying Louvain algorithm...
Louvain modularity: 0.4030652 
Trying Walktrap algorithm...
Walktrap modularity: 0.3498475 
Trying Label Propagation algorithm...
Label Propagation modularity: 0.006991183 
Selected Louvain algorithm with modularity 0.4030652 

Network Analysis Results for Bacteria_Alpine_Old 
Number of nodes: 3000 
Number of edges: 314698 
Positive edges: 314698 
Negative edges: 0 
Network density: 0.06995621 
Number of communities: 95 
Modularity: 0.4030652 
Selected 2714 hub nodes with degree > 6
Hub node information saved to hub_nodes_bacteria_alpine_old.csv 
Found 95 communities
Successfully plotted network with community colors
Community information saved to community_data_bacteria_alpine_old.csv 

Creating filtered visualization for Bacteria_Alpine_Old network...
Filtering nodes with degree < 5 and edges with weight < 0.65 
Filtered network visualization saved to network_bacteria_alpine_old_filtered.pdf
Filtered network node information saved to filtered_nodes_bacteria_alpine_old.csv 
Messaggi di avvertimento:
1: In layout_with_fr(filtered_network, area = vcount(filtered_network)^2.5,  :
  Argument `coolexp' is deprecated and has no effect
2: In layout_with_fr(filtered_network, area = vcount(filtered_network)^2.5,  :
  Argument `area' is deprecated and has no effect
3: In layout_with_fr(filtered_network, area = vcount(filtered_network)^2.5,  :
  Argument `repulserad' is deprecated and has no effect
> # Bacteria Alpine New
> if (!is.null(bacteria_alpine_new)) {
+   bacteria_alpine_new_cor <- create_correlation_matrix(bacteria_alpine_new, threshold = 0.6, max_otus = 3000)
+   bacteria_alpine_new_network <- analyze_network(bacteria_alpine_new_cor, bacteria_alpine_tax, "Bacteria_Alpine_New")
+ }

OTU table dimensions: 79887 58 
Warning: Removing 22437 constant rows (all zeros or all same value)
Dataset too large ( 57450  OTUs). Filtering to top 3000 most abundant OTUs
Filtered to 3000 OTUs based on abundance and variance
Estimated memory requirement: 0.07 GB
Calculating correlation matrix...

Before thresholding:
Total correlations: 9000000 
Positive correlations: 5370096 
Negative correlations: 3629486 
Range: -0.8878908 1 

After thresholding (|correlation| ≥ 0.6 ):
Total non-zero correlations: 144606 
Positive correlations: 144606 
Negative correlations: 0 
Range of non-zero correlations: 0.6000007 1 

Summary of correlations:
Total correlations: 144606 
Positive correlations: 144606 
Negative correlations: 0 
Range: 0.6000007 1 

Removing duplicate edges...
Successfully simplified the network
Detecting communities using multiple algorithms...
Trying Fast Greedy algorithm...
Error in Fast Greedy algorithm: At vendor/cigraph/src/community/fast_modularity.c:668 : Fast greedy community detection works only on graphs without multi-edges. Invalid value 
Trying Louvain algorithm...
Louvain modularity: 0.4456391 
Trying Walktrap algorithm...
Walktrap modularity: 0.3614362 
Trying Label Propagation algorithm...
Label Propagation modularity: 0.2906324 
Selected Louvain algorithm with modularity 0.4456391 

Network Analysis Results for Bacteria_Alpine_New 
Number of nodes: 3000 
Number of edges: 144606 
Positive edges: 144606 
Negative edges: 0 
Network density: 0.03214538 
Number of communities: 645 
Modularity: 0.4456391 
Selected 1922 hub nodes with degree > 6
Hub node information saved to hub_nodes_bacteria_alpine_new.csv 
Found 645 communities
Successfully plotted network with community colors
Community information saved to community_data_bacteria_alpine_new.csv 

Creating filtered visualization for Bacteria_Alpine_New network...
Filtering nodes with degree < 5 and edges with weight < 0.65 
Filtered network visualization saved to network_bacteria_alpine_new_filtered.pdf
Filtered network node information saved to filtered_nodes_bacteria_alpine_new.csv 
Messaggi di avvertimento:
1: In layout_with_fr(filtered_network, area = vcount(filtered_network)^2.5,  :
  Argument `coolexp' is deprecated and has no effect
2: In layout_with_fr(filtered_network, area = vcount(filtered_network)^2.5,  :
  Argument `area' is deprecated and has no effect
3: In layout_with_fr(filtered_network, area = vcount(filtered_network)^2.5,  :
  Argument `repulserad' is deprecated and has no effect
> # Bacteria Farming Old
> if (!is.null(bacteria_farming_old)) {
+   bacteria_farming_old_cor <- create_correlation_matrix(bacteria_farming_old, threshold = 0.6, max_otus = 3000)
+   bacteria_farming_old_network <- analyze_network(bacteria_farming_old_cor, bacteria_farming_tax, "Bacteria_Farming_Old")
+ }

OTU table dimensions: 58250 65 
Warning: Removing 25077 constant rows (all zeros or all same value)
Dataset too large ( 33173  OTUs). Filtering to top 3000 most abundant OTUs
Filtered to 3000 OTUs based on abundance and variance
Estimated memory requirement: 0.07 GB
Calculating correlation matrix...

Before thresholding:
Total correlations: 9000000 
Positive correlations: 5457264 
Negative correlations: 3542536 
Range: -0.8276743 1 

After thresholding (|correlation| ≥ 0.6 ):
Total non-zero correlations: 106396 
Positive correlations: 106396 
Negative correlations: 0 
Range of non-zero correlations: 0.6000093 0.9666605 

Summary of correlations:
Total correlations: 106396 
Positive correlations: 106396 
Negative correlations: 0 
Range: 0.6000093 0.9666605 

Removing duplicate edges...
Successfully simplified the network
Detecting communities using multiple algorithms...
Trying Fast Greedy algorithm...
Error in Fast Greedy algorithm: At vendor/cigraph/src/community/fast_modularity.c:668 : Fast greedy community detection works only on graphs without multi-edges. Invalid value 
Trying Louvain algorithm...
Louvain modularity: 0.5180827 
Trying Walktrap algorithm...
Walktrap modularity: 0.4723677 
Trying Label Propagation algorithm...
Label Propagation modularity: 0.3459182 
Selected Louvain algorithm with modularity 0.5180827 

Network Analysis Results for Bacteria_Farming_Old 
Number of nodes: 3000 
Number of edges: 106396 
Positive edges: 106396 
Negative edges: 0 
Network density: 0.02365144 
Number of communities: 662 
Modularity: 0.5180827 
Selected 1892 hub nodes with degree > 6
Hub node information saved to hub_nodes_bacteria_farming_old.csv 
Found 662 communities
Successfully plotted network with community colors
Community information saved to community_data_bacteria_farming_old.csv 

Creating filtered visualization for Bacteria_Farming_Old network...
Filtering nodes with degree < 5 and edges with weight < 0.65 
Filtered network visualization saved to network_bacteria_farming_old_filtered.pdf
Filtered network node information saved to filtered_nodes_bacteria_farming_old.csv 
Messaggi di avvertimento:
1: In layout_with_fr(filtered_network, area = vcount(filtered_network)^2.5,  :
  Argument `coolexp' is deprecated and has no effect
2: In layout_with_fr(filtered_network, area = vcount(filtered_network)^2.5,  :
  Argument `area' is deprecated and has no effect
3: In layout_with_fr(filtered_network, area = vcount(filtered_network)^2.5,  :
  Argument `repulserad' is deprecated and has no effect
> # Bacteria Farming New
> if (!is.null(bacteria_farming_new)) {
+   bacteria_farming_new_cor <- create_correlation_matrix(bacteria_farming_new, threshold = 0.6, max_otus = 3000)
+   bacteria_farming_new_network <- analyze_network(bacteria_farming_new_cor, bacteria_farming_tax, "Bacteria_Farming_New")
+ }

OTU table dimensions: 58250 65 
Warning: Removing 8555 constant rows (all zeros or all same value)
Dataset too large ( 49695  OTUs). Filtering to top 3000 most abundant OTUs
Filtered to 3000 OTUs based on abundance and variance
Estimated memory requirement: 0.07 GB
Calculating correlation matrix...

Before thresholding:
Total correlations: 9000000 
Positive correlations: 5113116 
Negative correlations: 3886570 
Range: -0.8007594 1 

After thresholding (|correlation| ≥ 0.6 ):
Total non-zero correlations: 129372 
Positive correlations: 129372 
Negative correlations: 0 
Range of non-zero correlations: 0.6 1 

Summary of correlations:
Total correlations: 129372 
Positive correlations: 129372 
Negative correlations: 0 
Range: 0.6 1 

Removing duplicate edges...
Successfully simplified the network
Detecting communities using multiple algorithms...
Trying Fast Greedy algorithm...
Error in Fast Greedy algorithm: At vendor/cigraph/src/community/fast_modularity.c:668 : Fast greedy community detection works only on graphs without multi-edges. Invalid value 
Trying Louvain algorithm...
Louvain modularity: 0.5130906 
Trying Walktrap algorithm...
Walktrap modularity: 0.449587 
Trying Label Propagation algorithm...
Label Propagation modularity: 0.4144496 
Selected Louvain algorithm with modularity 0.5130906 

Network Analysis Results for Bacteria_Farming_New 
Number of nodes: 3000 
Number of edges: 129372 
Positive edges: 129372 
Negative edges: 0 
Network density: 0.02875892 
Number of communities: 740 
Modularity: 0.5130906 
Selected 1839 hub nodes with degree > 6
Hub node information saved to hub_nodes_bacteria_farming_new.csv 
Found 740 communities
Successfully plotted network with community colors
Community information saved to community_data_bacteria_farming_new.csv 

Creating filtered visualization for Bacteria_Farming_New network...
Filtering nodes with degree < 5 and edges with weight < 0.65 
Filtered network visualization saved to network_bacteria_farming_new_filtered.pdf
Filtered network node information saved to filtered_nodes_bacteria_farming_new.csv 
Messaggi di avvertimento:
1: In layout_with_fr(filtered_network, area = vcount(filtered_network)^2.5,  :
  Argument `coolexp' is deprecated and has no effect
2: In layout_with_fr(filtered_network, area = vcount(filtered_network)^2.5,  :
  Argument `area' is deprecated and has no effect
3: In layout_with_fr(filtered_network, area = vcount(filtered_network)^2.5,  :
  Argument `repulserad' is deprecated and has no effect
> 