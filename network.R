# Load required libraries
library(readxl)
library(igraph)
library(vegan)
library(tidyverse)

# Read data files for the new experiment with four datasets
# Bacteria datasets
bacteria_alpine_otu <- read_excel("OTU_table_bacteria_alpine_samples.xlsx")
bacteria_farming_otu <- read_excel("OTU_table_bacteria_farming_samples.xlsx")

# Fungi datasets
fungi_alpine_otu <- read_excel("OTU_table_fungi_alpine_samples.xlsx")
fungi_farming_otu <- read_excel("OTU_table_fungi_farming_samples.xlsx")

# Taxonomy files
bacteria_alpine_tax <- read_excel("taxonomy_bacteria_alpine_samples.xlsx")
bacteria_farming_tax <- read_excel("taxonomy_bacteria_farming_samples.xlsx")
fungi_alpine_tax <- read_excel("taxonomy_fungi_alpine_samples.xlsx")
fungi_farming_tax <- read_excel("taxonomy_fungi_farming_samples.xlsx")

# Metadata files
bacteria_alpine_meta <- read_excel("metadata_bacteria_alpine_samples.xlsx")
bacteria_farming_meta <- read_excel("metadata_bacteria_farming_samples.xlsx")
fungi_alpine_meta <- read_excel("metadata_fungi_alpine_samples.xlsx")
fungi_farming_meta <- read_excel("metadata_fungi_farming_samples.xlsx")

# Print data dimensions for debugging
cat("Bacteria Alpine OTU dimensions:", dim(bacteria_alpine_otu), "\n")
cat("Bacteria Farming OTU dimensions:", dim(bacteria_farming_otu), "\n")
cat("Fungi Alpine OTU dimensions:", dim(fungi_alpine_otu), "\n")
cat("Fungi Farming OTU dimensions:", dim(fungi_farming_otu), "\n")

# Function to filter samples by environment (old vs new)
filter_samples_by_environment <- function(otu_table, metadata, environment_type) {
  # Determine the sample ID column name (different files use different names)
  sample_id_col <- NULL
  if ("Sample_ID" %in% colnames(metadata)) {
    sample_id_col <- "Sample_ID"
  } else if ("sample" %in% colnames(metadata)) {
    sample_id_col <- "sample"
  } else {
    cat("Error: Could not find sample ID column in metadata\n")
    cat("Available columns:", paste(colnames(metadata), collapse = ", "), "\n")
    return(NULL)
  }

  # Get sample IDs for the specified environment
  target_samples <- metadata[[sample_id_col]][grepl(environment_type, metadata$Environment, ignore.case = TRUE)]

  cat("Using sample ID column:", sample_id_col, "\n")
  cat("Found", length(target_samples), "target samples for environment", environment_type, "\n")

  # Find which columns in OTU table correspond to these samples
  # First column is typically OTU IDs, so we check from column 2 onwards
  sample_cols <- which(colnames(otu_table) %in% target_samples)

  if (length(sample_cols) == 0) {
    cat("Warning: No samples found for environment", environment_type, "\n")
    cat("Target samples:", paste(head(target_samples), collapse = ", "), "\n")
    cat("OTU table columns (first 10):", paste(colnames(otu_table)[1:min(10, ncol(otu_table))], collapse = ", "), "\n")
    return(NULL)
  }

  # Return OTU table with only the target samples (plus the first column with OTU IDs)
  filtered_table <- otu_table[, c(1, sample_cols)]
  cat("Filtered", environment_type, "samples:", ncol(filtered_table) - 1, "samples\n")
  return(filtered_table)
}

# Function to create correlation matrix with debugging and memory management
create_correlation_matrix <- function(otu_table, threshold = 0.6, max_otus = 5000) {
  # Print dimensions of input table
  cat("\nOTU table dimensions:", dim(otu_table), "\n")

  # Check if the table has at least one column besides the OTU IDs
  if (ncol(otu_table) <= 1) {
    stop("OTU table must have at least one sample column besides the OTU IDs")
  }

  # Convert to matrix and calculate correlations
  tryCatch({
    # Extract OTU matrix (remove first column which is likely OTU IDs)
    otu_matrix <- as.matrix(otu_table[,-1])

    # Check for rows with all zeros or constant values
    row_sums <- rowSums(otu_matrix)
    constant_rows <- row_sums == 0 | apply(otu_matrix, 1, function(x) length(unique(x)) <= 1)
    if (any(constant_rows)) {
      cat("Warning: Removing", sum(constant_rows), "constant rows (all zeros or all same value)\n")
      otu_matrix <- otu_matrix[!constant_rows, , drop = FALSE]
    }

    # Check if we still have enough rows
    if (nrow(otu_matrix) <= 1) {
      stop("Not enough variable rows for correlation analysis after removing constant rows")
    }

    # Memory management: If dataset is too large, filter to most abundant/variable OTUs
    if (nrow(otu_matrix) > max_otus) {
      cat("Dataset too large (", nrow(otu_matrix), " OTUs). Filtering to top", max_otus, "most abundant OTUs\n")

      # Calculate abundance and variance for each OTU
      otu_abundance <- rowMeans(otu_matrix)
      otu_variance <- apply(otu_matrix, 1, var)

      # Create a combined score (abundance + variance, both normalized)
      abundance_norm <- (otu_abundance - min(otu_abundance)) / (max(otu_abundance) - min(otu_abundance))
      variance_norm <- (otu_variance - min(otu_variance)) / (max(otu_variance) - min(otu_variance))
      combined_score <- abundance_norm + variance_norm

      # Select top OTUs based on combined score
      top_otus <- order(combined_score, decreasing = TRUE)[1:max_otus]
      otu_matrix <- otu_matrix[top_otus, , drop = FALSE]

      cat("Filtered to", nrow(otu_matrix), "OTUs based on abundance and variance\n")
    }

    # Estimate memory requirement
    n_otus <- nrow(otu_matrix)
    memory_gb <- (n_otus^2 * 8) / (1024^3)  # 8 bytes per double, convert to GB
    cat("Estimated memory requirement:", round(memory_gb, 2), "GB\n")

    if (memory_gb > 8) {
      cat("Warning: Large memory requirement. Consider reducing max_otus parameter.\n")
    }

    # Calculate correlations with pairwise.complete.obs to handle NAs
    cat("Calculating correlation matrix...\n")
    cor_matrix <- cor(t(otu_matrix), method = "spearman", use = "pairwise.complete.obs")

    # Replace any NAs with 0
    if (any(is.na(cor_matrix))) {
      cat("Warning: Replacing", sum(is.na(cor_matrix)), "NA values with 0\n")
      cor_matrix[is.na(cor_matrix)] <- 0
    }

    # Print summary before thresholding
    cat("\nBefore thresholding:\n")
    cat("Total correlations:", length(cor_matrix), "\n")
    cat("Positive correlations:", sum(cor_matrix > 0), "\n")
    cat("Negative correlations:", sum(cor_matrix < 0), "\n")
    cat("Range:", range(cor_matrix), "\n")

    # Store signs before thresholding
    signs <- sign(cor_matrix)

    # Apply threshold to absolute values
    cor_matrix[abs(cor_matrix) < threshold] <- 0

    # Restore signs
    cor_matrix <- cor_matrix * signs

    # Set diagonal to zero
    diag(cor_matrix) <- 0

    # Print summary after thresholding
    cat("\nAfter thresholding (|correlation| ≥", threshold, "):\n")
    cat("Total non-zero correlations:", sum(cor_matrix != 0), "\n")
    cat("Positive correlations:", sum(cor_matrix > 0), "\n")
    cat("Negative correlations:", sum(cor_matrix < 0), "\n")

    if (sum(cor_matrix != 0) > 0) {
      cat("Range of non-zero correlations:", range(cor_matrix[cor_matrix != 0]), "\n")
    } else {
      cat("No correlations above threshold.\n")
    }

    # Create filtered indices that account for both constant row removal and abundance filtering
    original_indices <- which(!constant_rows)
    if (exists("top_otus")) {
      # If we filtered by abundance, map back through both filters
      final_indices <- original_indices[top_otus]
    } else {
      final_indices <- original_indices
    }

    # Return the correlation matrix and the filtered row indices
    # This will help with mapping back to the original OTU IDs
    return(list(
      cor_matrix = cor_matrix,
      filtered_indices = final_indices,
      n_original_otus = nrow(otu_table),
      n_filtered_otus = nrow(otu_matrix)
    ))
  }, error = function(e) {
    cat("Error in correlation calculation:", e$message, "\n")
    # Return a smaller fallback matrix to avoid memory issues
    n_rows <- min(1000, nrow(otu_table))
    return(list(
      cor_matrix = matrix(0, nrow = n_rows, ncol = n_rows),
      filtered_indices = integer(0),
      n_original_otus = nrow(otu_table),
      n_filtered_otus = 0
    ))
  })
}

# Function to analyze network
analyze_network <- function(cor_result, taxonomy_data, group_name) {
  # Extract correlation matrix from result
  if (is.list(cor_result) && "cor_matrix" %in% names(cor_result)) {
    cor_matrix <- cor_result$cor_matrix
    filtered_indices <- cor_result$filtered_indices
  } else {
    # For backward compatibility
    cor_matrix <- cor_result
  }
  # Get non-zero correlations
  edges <- which(cor_matrix != 0, arr.ind = TRUE)
  weights <- cor_matrix[edges]

  cat("\nSummary of correlations:\n")
  cat("Total correlations:", length(weights), "\n")
  cat("Positive correlations:", sum(weights > 0), "\n")
  cat("Negative correlations:", sum(weights < 0), "\n")
  cat("Range:", range(weights), "\n")

  # Create edge list
  edge_list <- data.frame(
    from = edges[,1],
    to = edges[,2],
    weight = weights
  )

  # Remove duplicate edges (both directions) by keeping the strongest correlation
  edge_list <- edge_list %>%
    mutate(
      from_min = pmin(from, to),
      to_max = pmax(from, to)
    ) %>%
    group_by(from_min, to_max) %>%
    slice_max(abs(weight), n = 1) %>%
    ungroup() %>%
    select(from, to, weight)

  # Create network
  network <- graph_from_data_frame(
    d = edge_list,
    directed = FALSE,
    vertices = 1:nrow(cor_matrix)
  )

  # Remove duplicate edges before creating the network
  # This is crucial for cluster_fast_greedy which doesn't work with multi-edges
  cat("\nRemoving duplicate edges...\n")

  # Use igraph's simplify function to remove multi-edges and self-loops
  # Try different approaches based on the version of igraph
  tryCatch({
    if ("remove.multiple" %in% names(formals(simplify))) {
      network <- simplify(network, remove.multiple = TRUE, remove.loops = TRUE)
    } else {
      # For newer versions of igraph
      network <- simplify(network)
    }
    cat("Successfully simplified the network\n")
  }, error = function(e) {
    cat("Error in simplify:", e$message, "\n")
    cat("Trying alternative approach...\n")

    # Alternative approach: manually remove duplicate edges
    # Get edge list
    el <- as_data_frame(network, what = "edges")
    # Create a unique identifier for each edge (regardless of direction)
    el$edge_id <- apply(cbind(pmin(el$from, el$to), pmax(el$from, el$to)), 1,
                        function(x) paste(x, collapse = "_"))
    # Keep only unique edges
    el_unique <- el[!duplicated(el$edge_id),]
    # Recreate the network
    network <- graph_from_data_frame(el_unique[, c("from", "to", "weight")],
                                    directed = FALSE,
                                    vertices = 1:nrow(cor_matrix))
    cat("Network recreated without multi-edges\n")
  })

  # Calculate communities with multiple algorithms and select the best one
  cat("Detecting communities using multiple algorithms...\n")

  # Initialize variables to track the best community detection result
  communities <- NULL
  best_modularity <- -1
  best_algorithm <- "none"

  # Function to safely try a community detection algorithm and calculate modularity
  try_community_detection <- function(algorithm_name, algorithm_func, ...) {
    tryCatch({
      cat("Trying", algorithm_name, "algorithm...\n")
      comm <- algorithm_func(network, ...)

      # Extract membership vector safely
      mem <- NULL
      tryCatch({
        if (is.function(membership)) {
          mem <- membership(comm)
        } else if (inherits(comm, "communities")) {
          mem <- comm$membership
        } else {
          mem <- comm
        }

        # Ensure it's a numeric vector
        if (is.list(mem)) mem <- unlist(mem)
        mem <- as.numeric(mem)

        # Calculate modularity
        mod <- modularity(network, mem)
        cat(algorithm_name, "modularity:", mod, "\n")

        return(list(communities = comm, modularity = mod))
      }, error = function(e) {
        cat("Error extracting membership or calculating modularity for", algorithm_name, ":", e$message, "\n")
        return(NULL)
      })
    }, error = function(e) {
      cat("Error in", algorithm_name, "algorithm:", e$message, "\n")
      return(NULL)
    })
  }

  # Try different community detection algorithms

  # 1. Fast Greedy algorithm (works well for medium-sized networks)
  if (vcount(network) < 5000) {
    fg_result <- try_community_detection("Fast Greedy", cluster_fast_greedy, weights = abs(E(network)$weight))
    if (!is.null(fg_result) && fg_result$modularity > best_modularity) {
      best_modularity <- fg_result$modularity
      communities <- fg_result$communities
      best_algorithm <- "Fast Greedy"
    }
  }

  # 2. Louvain algorithm (works well for larger networks)
  louvain_result <- try_community_detection("Louvain", cluster_louvain, weights = abs(E(network)$weight))
  if (!is.null(louvain_result) && louvain_result$modularity > best_modularity) {
    best_modularity <- louvain_result$modularity
    communities <- louvain_result$communities
    best_algorithm <- "Louvain"
  }

  # 3. Walktrap algorithm (works well for most networks)
  wt_result <- try_community_detection("Walktrap", cluster_walktrap, weights = abs(E(network)$weight))
  if (!is.null(wt_result) && wt_result$modularity > best_modularity) {
    best_modularity <- wt_result$modularity
    communities <- wt_result$communities
    best_algorithm <- "Walktrap"
  }

  # 4. Label Propagation algorithm (fast but less stable)
  lp_result <- try_community_detection("Label Propagation", cluster_label_prop, weights = abs(E(network)$weight))
  if (!is.null(lp_result) && lp_result$modularity > best_modularity) {
    best_modularity <- lp_result$modularity
    communities <- lp_result$communities
    best_algorithm <- "Label Propagation"
  }

  # 5. Infomap algorithm (good for finding small communities)
  if (vcount(network) < 3000) {
    infomap_result <- try_community_detection("Infomap", cluster_infomap, e.weights = abs(E(network)$weight))
    if (!is.null(infomap_result) && infomap_result$modularity > best_modularity) {
      best_modularity <- infomap_result$modularity
      communities <- infomap_result$communities
      best_algorithm <- "Infomap"
    }
  }

  # If all algorithms failed, create a fallback community structure
  if (is.null(communities)) {
    cat("All community detection algorithms failed. Creating fallback community structure.\n")
    communities <- make_clusters(network, membership = rep(1, vcount(network)))
    best_algorithm <- "Fallback (single community)"
    best_modularity <- 0
  } else {
    cat("Selected", best_algorithm, "algorithm with modularity", best_modularity, "\n")
  }

  # Network analysis results
  cat("\nNetwork Analysis Results for", group_name, "\n")
  cat("Number of nodes:", vcount(network), "\n")
  cat("Number of edges:", ecount(network), "\n")
  cat("Positive edges:", sum(E(network)$weight > 0), "\n")
  cat("Negative edges:", sum(E(network)$weight < 0), "\n")
  cat("Network density:", edge_density(network), "\n")

  # Handle community statistics safely
  community_count <- 0
  mod_value <- 0

  # Initialize community count and modularity
  community_count <- 1
  mod_value <- 0

  tryCatch({
    # For newer versions of igraph, membership() is a function not an attribute
    if (is.function(membership)) {
      comm_membership <- membership(communities)
    } else if (inherits(communities, "communities")) {
      comm_membership <- communities$membership
    } else {
      # Direct access
      comm_membership <- communities
    }

    # Convert to numeric vector if needed
    if (is.list(comm_membership)) {
      comm_membership <- unlist(comm_membership)
    }

    # Ensure it's a numeric vector
    comm_membership <- as.numeric(comm_membership)

    community_count <- length(unique(comm_membership))
    cat("Number of communities:", community_count, "\n")

    # Calculate modularity safely
    tryCatch({
      mod_value <- modularity(network, comm_membership)
      cat("Modularity:", mod_value, "\n")
    }, error = function(e) {
      cat("Error calculating modularity:", e$message, "\n")
      mod_value <- 0
    })
  }, error = function(e) {
    cat("Error getting community statistics:", e$message, "\n")
    cat("Skipping community statistics\n")
    community_count <- 1
    mod_value <- 0
  })

  # Create network plot
  plot_title <- paste("Network for", group_name)
  pdf(paste0("network_", tolower(group_name), ".pdf"))

  # Edge colors based on correlation sign
  edge_colors <- ifelse(E(network)$weight > 0, "blue", "red")
  edge_widths <- abs(E(network)$weight) * 2  # Scale edge widths by correlation strength

  # Store edge weights for potential filtering
  E(network)$abs_weight <- abs(E(network)$weight)

  # Calculate node properties for visualization
  # Degree centrality (number of connections)
  node_degrees <- degree(network)

  # Betweenness centrality (importance as bridge between communities)
  node_betweenness <- betweenness(network, normalized = TRUE)

  # Scale node sizes based on degree (more connections = larger node)
  # Use log scale to prevent extremely large nodes
  min_size <- 2
  max_size <- 10
  if (max(node_degrees) > min(node_degrees)) {
    node_sizes <- min_size + (max_size - min_size) *
                  (node_degrees - min(node_degrees)) / (max(node_degrees) - min(node_degrees))
  } else {
    node_sizes <- rep(min_size, length(node_degrees))
  }

  # Identify hub nodes (nodes with degree > 6 as requested)
  # Find nodes with degree greater than 6
  hub_nodes <- which(node_degrees > 6)
  # Sort hub nodes by degree in descending order
  hub_nodes <- hub_nodes[order(node_degrees[hub_nodes], decreasing = TRUE)]
  cat("Selected", length(hub_nodes), "hub nodes with degree > 6\n")

  # Create node labels (only for hub nodes)
  node_labels <- rep(NA, vcount(network))

  # Export hub node taxonomy to a separate file
  if (length(hub_nodes) > 0) {
    # Create a data frame to store hub node information
    hub_data <- data.frame(
      Node_ID = hub_nodes,
      Group = rep(group_name, length(hub_nodes)),
      Degree = node_degrees[hub_nodes],
      Betweenness = node_betweenness[hub_nodes],
      OTU_ID = NA,
      stringsAsFactors = FALSE
    )

    # Add OTU ID information if available
    if (!is.null(taxonomy_data) && nrow(taxonomy_data) > 0) {
      # Get the original indices (before filtering constant rows)
      if (exists("filtered_indices") && length(filtered_indices) > 0) {
        # Map network node IDs back to original OTU indices
        original_indices <- filtered_indices[as.numeric(hub_nodes)]

        # Check if indices are within bounds
        valid_indices <- original_indices <= nrow(taxonomy_data)
        if (any(valid_indices)) {
          # Add OTU ID (first column of taxonomy data)
          for (i in seq_along(hub_nodes)) {
            if (valid_indices[i]) {
              idx <- original_indices[i]
              hub_data$OTU_ID[i] <- as.character(taxonomy_data[idx, 1])
            }
          }

          # Get taxonomy information for hub nodes (for labels)
          if ("Genus" %in% colnames(taxonomy_data)) {
            hub_labels <- taxonomy_data$Genus[original_indices[valid_indices]]
          } else if ("genus" %in% colnames(taxonomy_data)) {
            hub_labels <- taxonomy_data$genus[original_indices[valid_indices]]
          } else {
            # Use the first column as fallback
            hub_labels <- as.character(taxonomy_data[original_indices[valid_indices], 1])
          }

          # Assign labels to hub nodes (for potential use)
          node_labels[hub_nodes[valid_indices]] <- hub_labels
        }
      }
    }

    # If no taxonomy data or mapping failed, use node IDs
    if (all(is.na(node_labels[hub_nodes]))) {
      node_labels[hub_nodes] <- paste("Node", hub_nodes)
    }

    # Sort hub nodes by degree (descending)
    hub_data <- hub_data[order(hub_data$Degree, decreasing = TRUE), ]

    # Save hub node information to a CSV file
    hub_file <- paste0("hub_nodes_", tolower(group_name), ".csv")
    write.csv(hub_data, hub_file, row.names = FALSE)
    cat("Hub node information saved to", hub_file, "\n")
  }

  # Use a consistent approach for visualization across all groups
  # First create a basic plot with node sizes based on degree but no labels
  plot(network,
       vertex.size = node_sizes,
       vertex.label = NA,  # Remove labels as requested
       edge.color = edge_colors,
       edge.width = edge_widths,
       main = plot_title)

  # Add community visualization with improved coloring
  tryCatch({
    # Get community membership safely using the same approach as above
    if (is.function(membership)) {
      comm_membership <- membership(communities)
    } else if (inherits(communities, "communities")) {
      comm_membership <- communities$membership
    } else {
      # Direct access
      comm_membership <- communities
    }

    # Convert to numeric vector if needed
    if (is.list(comm_membership)) {
      comm_membership <- unlist(comm_membership)
    }

    # Ensure it's a numeric vector
    comm_membership <- as.numeric(comm_membership)

    # Get unique communities and count them
    unique_communities <- sort(unique(comm_membership))
    community_count <- length(unique_communities)

    cat("Found", community_count, "communities\n")

    # Generate better colors for communities
    # Use a more visually distinct color palette for better differentiation
    if (community_count <= 8) {
      # Use ColorBrewer palette for small number of communities
      community_colors <- c("#E41A1C", "#377EB8", "#4DAF4A", "#984EA3",
                           "#FF7F00", "#FFFF33", "#A65628", "#F781BF")
      community_colors <- community_colors[1:community_count]
    } else if (community_count <= 20) {
      # Use a larger distinct palette for medium number of communities
      community_colors <- c("#E41A1C", "#377EB8", "#4DAF4A", "#984EA3",
                           "#FF7F00", "#FFFF33", "#A65628", "#F781BF",
                           "#66C2A5", "#FC8D62", "#8DA0CB", "#E78AC3",
                           "#A6D854", "#FFD92F", "#E5C494", "#B3B3B3",
                           "#8DD3C7", "#BEBADA", "#FB8072", "#80B1D3")
      community_colors <- community_colors[1:community_count]
    } else {
      # For many communities, use a continuous color palette
      community_colors <- colorRampPalette(c("#2166AC", "#67A9CF", "#D1E5F0",
                                           "#F7F7F7", "#FDDBC7", "#EF8A62",
                                           "#B2182B"))(community_count)
    }

    # Create a mapping from community IDs to color indices
    color_mapping <- setNames(seq_along(unique_communities), unique_communities)

    # Color vertices by community (with error handling)
    if (length(comm_membership) == vcount(network)) {
      # Map each community to its color
      V(network)$color <- community_colors[color_mapping[comm_membership]]
      V(network)$community <- comm_membership  # Store community membership for reference
    } else {
      # If lengths don't match, use a default coloring
      V(network)$color <- "lightblue"
      cat("Warning: Community membership length doesn't match network size\n")
    }

    # Plot again with community colors and node sizes but no labels
    plot(network,
         vertex.size = node_sizes,
         vertex.label = NA,  # Remove labels as requested
         vertex.color = V(network)$color,
         edge.color = edge_colors,
         edge.width = edge_widths,
         layout = layout_with_fr(network),  # Use Fruchterman-Reingold layout for better community visualization
         main = paste(plot_title, "(colored by community)"))

    cat("Successfully plotted network with community colors\n")

    # Add community legend if not too many communities
    if (community_count > 1 && community_count <= 15) {  # Increased from 10 to 15
      # Count nodes in each community for the legend
      community_sizes <- table(comm_membership)

      # Create legend text with community sizes
      legend_text <- paste("Community", unique_communities,
                          "(", community_sizes[as.character(unique_communities)], "nodes)")

      # Add the legend with community sizes
      legend("topright",
             legend = legend_text,
             fill = community_colors,
             cex = 0.6,
             title = paste("Communities -", best_algorithm, "algorithm"))
    } else if (community_count > 15) {
      # For many communities, just show a summary
      legend("topright",
             legend = paste(community_count, "communities detected"),
             cex = 0.8,
             title = paste(best_algorithm, "algorithm"))
    }

    # Export community information to a separate file
    if (community_count > 1) {
      # Create a data frame with community information
      community_data <- data.frame(
        Node_ID = 1:vcount(network),
        Group = rep(group_name, vcount(network)),
        Community = comm_membership,
        Degree = node_degrees,
        Betweenness = node_betweenness,
        OTU_ID = NA,
        stringsAsFactors = FALSE
      )

      # Add OTU ID information if available
      if (!is.null(taxonomy_data) && nrow(taxonomy_data) > 0) {
        # Get the original indices (before filtering constant rows)
        if (exists("filtered_indices") && length(filtered_indices) > 0) {
          # Map network node IDs back to original OTU indices
          original_indices <- filtered_indices[1:vcount(network)]

          # Check if indices are within bounds
          valid_indices <- original_indices <= nrow(taxonomy_data)
          if (any(valid_indices)) {
            # Add OTU ID (first column of taxonomy data)
            for (i in 1:vcount(network)) {
              if (valid_indices[i]) {
                idx <- original_indices[i]
                community_data$OTU_ID[i] <- as.character(taxonomy_data[idx, 1])
              }
            }
          }
        }
      }

      # Sort by community and then by degree (descending) within each community
      community_data <- community_data[order(community_data$Community, -community_data$Degree), ]

      # Save community information to a CSV file
      community_file <- paste0("community_data_", tolower(group_name), ".csv")
      write.csv(community_data, community_file, row.names = FALSE)
      cat("Community information saved to", community_file, "\n")
    }
  }, error = function(e) {
    cat("Note: Could not add community colors:", e$message, "\n")
  })

  # Add legend for edges and nodes
  legend("bottomright",
         legend = c("Positive correlation", "Negative correlation", "Hub node (high degree)", "Regular node"),
         col = c("blue", "red", "black", "black"),
         pch = c(NA, NA, 19, 19),
         pt.cex = c(NA, NA, 2, 1),
         lty = c(1, 1, NA, NA),
         cex = 0.8)

  # Close the PDF device
  dev.off()

  # Create a filtered visualization for bacteria networks (or other large networks)
  if (group_name == "Bacteria" || vcount(network) > 1000) {
    cat("\nCreating filtered visualization for", group_name, "network...\n")

    # Create a filtered network based on node degree and edge weight
    # For bacteria networks, we'll filter more aggressively
    degree_threshold <- ifelse(group_name == "Bacteria", 10, 5)
    weight_threshold <- ifelse(group_name == "Bacteria", 0.7, 0.65)

    cat("Filtering nodes with degree <", degree_threshold, "and edges with weight <", weight_threshold, "\n")

    # Create a copy of the network for filtering
    filtered_network <- network

    # First, identify nodes to keep (those with high degree)
    high_degree_nodes <- which(node_degrees >= degree_threshold)

    # If we have too few nodes after filtering, adjust the threshold
    if (length(high_degree_nodes) < 50) {
      # Find a threshold that keeps at least 50 nodes or 10% of the original nodes
      min_nodes <- min(50, ceiling(vcount(network) * 0.1))
      sorted_degrees <- sort(node_degrees, decreasing = TRUE)
      if (length(sorted_degrees) >= min_nodes) {
        degree_threshold <- sorted_degrees[min_nodes]
        high_degree_nodes <- which(node_degrees >= degree_threshold)
        cat("Adjusted degree threshold to", degree_threshold, "to keep at least", min_nodes, "nodes\n")
      }
    }

    # Create a subgraph with only the high-degree nodes
    if (length(high_degree_nodes) > 0) {
      filtered_network <- induced_subgraph(network, high_degree_nodes)

      # Further filter edges by weight
      edges_to_remove <- which(E(filtered_network)$abs_weight < weight_threshold)
      if (length(edges_to_remove) > 0) {
        filtered_network <- delete_edges(filtered_network, edges_to_remove)
      }

      # Remove isolated nodes (degree 0 after edge filtering)
      isolated_nodes <- which(degree(filtered_network) == 0)
      if (length(isolated_nodes) > 0) {
        filtered_network <- delete_vertices(filtered_network, isolated_nodes)
      }

      # Only proceed if we have nodes and edges left
      if (vcount(filtered_network) > 0 && ecount(filtered_network) > 0) {
        # Create a PDF for the filtered network
        pdf(paste0("network_", tolower(group_name), "_filtered.pdf"))

        # Recalculate node properties for the filtered network
        filtered_node_degrees <- degree(filtered_network)

        # Scale node sizes based on degree
        min_size <- 3  # Slightly larger minimum size for better visibility
        max_size <- 12
        if (max(filtered_node_degrees) > min(filtered_node_degrees)) {
          filtered_node_sizes <- min_size + (max_size - min_size) *
                          (filtered_node_degrees - min(filtered_node_degrees)) /
                          (max(filtered_node_degrees) - min(filtered_node_degrees))
        } else {
          filtered_node_sizes <- rep(min_size, length(filtered_node_degrees))
        }

        # Edge properties
        filtered_edge_colors <- ifelse(E(filtered_network)$weight > 0, "blue", "red")
        filtered_edge_widths <- abs(E(filtered_network)$weight) * 3  # Slightly thicker for better visibility

        # Plot the filtered network
        # Create a layout with longer edges to spread out nodes
        # Use Fruchterman-Reingold layout with increased area parameter
        fr_layout <- layout_with_fr(filtered_network,
                                   area = vcount(filtered_network)^2.5, # Increase area to spread nodes
                                   repulserad = vcount(filtered_network)^3, # Increase repulsion radius
                                   coolexp = 1.5) # Slower cooling for better layout

        # Plot with standard layout
        plot(filtered_network,
             vertex.size = filtered_node_sizes,
             vertex.label = NA,
             edge.color = filtered_edge_colors,
             edge.width = filtered_edge_widths,
             layout = layout_with_fr(filtered_network),
             main = paste(plot_title, "(filtered view)"))

        # Plot with expanded layout
        plot(filtered_network,
             vertex.size = filtered_node_sizes,
             vertex.label = NA,
             edge.color = filtered_edge_colors,
             edge.width = filtered_edge_widths,
             layout = fr_layout,
             main = paste(plot_title, "(filtered view, expanded layout)"))

        # If the filtered network has community structure, color by community
        if ("community" %in% vertex_attr_names(filtered_network)) {
          # Get community membership
          filtered_comm_membership <- V(filtered_network)$community

          # Get unique communities
          unique_communities <- sort(unique(filtered_comm_membership))
          community_count <- length(unique_communities)

          # Generate colors for communities
          if (community_count <= 20) {
            community_colors <- c("#E41A1C", "#377EB8", "#4DAF4A", "#984EA3",
                                 "#FF7F00", "#FFFF33", "#A65628", "#F781BF",
                                 "#66C2A5", "#FC8D62", "#8DA0CB", "#E78AC3",
                                 "#A6D854", "#FFD92F", "#E5C494", "#B3B3B3",
                                 "#8DD3C7", "#BEBADA", "#FB8072", "#80B1D3")
            community_colors <- community_colors[1:min(community_count, length(community_colors))]
          } else {
            community_colors <- colorRampPalette(c("#2166AC", "#67A9CF", "#D1E5F0",
                                                 "#F7F7F7", "#FDDBC7", "#EF8A62",
                                                 "#B2182B"))(community_count)
          }

          # Create a mapping from community IDs to color indices
          color_mapping <- setNames(seq_along(unique_communities), unique_communities)

          # Assign colors to vertices
          V(filtered_network)$color <- community_colors[color_mapping[filtered_comm_membership]]

          # Plot with community colors - standard layout
          plot(filtered_network,
               vertex.size = filtered_node_sizes,
               vertex.label = NA,
               vertex.color = V(filtered_network)$color,
               edge.color = filtered_edge_colors,
               edge.width = filtered_edge_widths,
               layout = layout_with_fr(filtered_network),
               main = paste(plot_title, "(filtered view, colored by community)"))

          # Plot with community colors - expanded layout
          plot(filtered_network,
               vertex.size = filtered_node_sizes,
               vertex.label = NA,
               vertex.color = V(filtered_network)$color,
               edge.color = filtered_edge_colors,
               edge.width = filtered_edge_widths,
               layout = fr_layout,
               main = paste(plot_title, "(filtered view, colored by community, expanded layout)"))

          # Add legend if not too many communities
          if (community_count <= 15) {
            # Count nodes in each community
            community_sizes <- table(filtered_comm_membership)

            # Create legend text
            legend_text <- paste("Community", unique_communities,
                               "(", community_sizes[as.character(unique_communities)], "nodes)")

            # Add legend
            legend("topright",
                   legend = legend_text,
                   fill = community_colors[1:community_count],
                   cex = 0.6,
                   title = "Communities")
          }
        }

        # Add legend for edges and nodes
        legend("bottomright",
               legend = c("Positive correlation", "Negative correlation", "Hub node", "Regular node"),
               col = c("blue", "red", "black", "black"),
               pch = c(NA, NA, 19, 19),
               pt.cex = c(NA, NA, 2, 1),
               lty = c(1, 1, NA, NA),
               cex = 0.8)

        # Close the PDF
        dev.off()

        cat("Filtered network visualization saved to network_", tolower(group_name), "_filtered.pdf\n", sep="")

        # Save filtered network node information
        filtered_nodes_file <- paste0("filtered_nodes_", tolower(group_name), ".csv")
        filtered_nodes_data <- data.frame(
          Node_ID = as.numeric(names(V(filtered_network))),
          Degree = filtered_node_degrees,
          Original_Node_ID = V(filtered_network)$name,
          stringsAsFactors = FALSE
        )
        write.csv(filtered_nodes_data, filtered_nodes_file, row.names = FALSE)
        cat("Filtered network node information saved to", filtered_nodes_file, "\n")
      } else {
        cat("After filtering, no nodes or edges remain. Skipping filtered visualization.\n")
      }
    } else {
      cat("No nodes meet the filtering criteria. Skipping filtered visualization.\n")
    }
  }

  # Return network analysis results
  return(list(
    network = network,
    communities = communities,
    degree = node_degrees,
    betweenness = node_betweenness,
    closeness = closeness(network, weights = abs(E(network)$weight)),
    hub_nodes = hub_nodes,
    node_sizes = node_sizes,
    community_count = community_count,
    modularity = mod_value
  ))
}



# Create filtered datasets for old and new samples
cat("\n========== FILTERING SAMPLES BY ENVIRONMENT ==========\n")

# Bacteria Alpine
bacteria_alpine_old <- filter_samples_by_environment(bacteria_alpine_otu, bacteria_alpine_meta, "old")
bacteria_alpine_new <- filter_samples_by_environment(bacteria_alpine_otu, bacteria_alpine_meta, "new")

# Bacteria Farming
bacteria_farming_old <- filter_samples_by_environment(bacteria_farming_otu, bacteria_farming_meta, "old")
bacteria_farming_new <- filter_samples_by_environment(bacteria_farming_otu, bacteria_farming_meta, "new")

# Fungi Alpine
fungi_alpine_old <- filter_samples_by_environment(fungi_alpine_otu, fungi_alpine_meta, "old")
fungi_alpine_new <- filter_samples_by_environment(fungi_alpine_otu, fungi_alpine_meta, "new")

# Fungi Farming
fungi_farming_old <- filter_samples_by_environment(fungi_farming_otu, fungi_farming_meta, "old")
fungi_farming_new <- filter_samples_by_environment(fungi_farming_otu, fungi_farming_meta, "new")

# Analyze each group with threshold 0.6 as requested
cat("\n========== INDIVIDUAL NETWORK ANALYSES ==========\n")

# Bacteria Alpine Old (use smaller max_otus for large datasets)
if (!is.null(bacteria_alpine_old)) {
  bacteria_alpine_old_cor <- create_correlation_matrix(bacteria_alpine_old, threshold = 0.6, max_otus = 3000)
  bacteria_alpine_old_network <- analyze_network(bacteria_alpine_old_cor, bacteria_alpine_tax, "Bacteria_Alpine_Old")
}

# Bacteria Alpine New
if (!is.null(bacteria_alpine_new)) {
  bacteria_alpine_new_cor <- create_correlation_matrix(bacteria_alpine_new, threshold = 0.6, max_otus = 3000)
  bacteria_alpine_new_network <- analyze_network(bacteria_alpine_new_cor, bacteria_alpine_tax, "Bacteria_Alpine_New")
}

# Bacteria Farming Old
if (!is.null(bacteria_farming_old)) {
  bacteria_farming_old_cor <- create_correlation_matrix(bacteria_farming_old, threshold = 0.6, max_otus = 3000)
  bacteria_farming_old_network <- analyze_network(bacteria_farming_old_cor, bacteria_farming_tax, "Bacteria_Farming_Old")
}

# Bacteria Farming New
if (!is.null(bacteria_farming_new)) {
  bacteria_farming_new_cor <- create_correlation_matrix(bacteria_farming_new, threshold = 0.6, max_otus = 3000)
  bacteria_farming_new_network <- analyze_network(bacteria_farming_new_cor, bacteria_farming_tax, "Bacteria_Farming_New")
}

# Fungi Alpine Old (fungi datasets are smaller, can handle more OTUs)
if (!is.null(fungi_alpine_old)) {
  fungi_alpine_old_cor <- create_correlation_matrix(fungi_alpine_old, threshold = 0.6, max_otus = 6000)
  fungi_alpine_old_network <- analyze_network(fungi_alpine_old_cor, fungi_alpine_tax, "Fungi_Alpine_Old")
}

# Fungi Alpine New
if (!is.null(fungi_alpine_new)) {
  fungi_alpine_new_cor <- create_correlation_matrix(fungi_alpine_new, threshold = 0.6, max_otus = 6000)
  fungi_alpine_new_network <- analyze_network(fungi_alpine_new_cor, fungi_alpine_tax, "Fungi_Alpine_New")
}

# Fungi Farming Old
if (!is.null(fungi_farming_old)) {
  fungi_farming_old_cor <- create_correlation_matrix(fungi_farming_old, threshold = 0.6, max_otus = 6000)
  fungi_farming_old_network <- analyze_network(fungi_farming_old_cor, fungi_farming_tax, "Fungi_Farming_Old")
}

# Fungi Farming New
if (!is.null(fungi_farming_new)) {
  fungi_farming_new_cor <- create_correlation_matrix(fungi_farming_new, threshold = 0.6, max_otus = 6000)
  fungi_farming_new_network <- analyze_network(fungi_farming_new_cor, fungi_farming_tax, "Fungi_Farming_New")
}


# Compare network properties
cat("\n\n========== NETWORK COMPARISON ==========\n")
tryCatch({
  # Create a list to store network data
  network_data <- list()

  # Helper function to add network data
  add_network_data <- function(network_obj, otu_count, group_name) {
    if (exists(deparse(substitute(network_obj))) && !is.null(network_obj)) {
      # Calculate normalized metrics
      nodes_count <- vcount(network_obj$network)
      edges_count <- ecount(network_obj$network)

      network_data[[group_name]] <<- list(
        # Raw metrics
        Nodes = nodes_count,
        Edges = edges_count,
        Avg_Degree = mean(network_obj$degree),
        Max_Degree = max(network_obj$degree),
        Avg_Betweenness = mean(network_obj$betweenness),
        Avg_Closeness = mean(network_obj$closeness),
        Communities = network_obj$community_count,
        Modularity = network_obj$modularity,
        Hub_Nodes = length(network_obj$hub_nodes),

        # Normalized metrics (by OTU count)
        Norm_Nodes = nodes_count / otu_count,
        Norm_Edges = edges_count / (otu_count * (otu_count - 1) / 2),
        Norm_Avg_Degree = mean(network_obj$degree) / otu_count,
        Norm_Max_Degree = max(network_obj$degree) / otu_count,
        Norm_Communities = network_obj$community_count / nodes_count,
        Network_Density = edge_density(network_obj$network)
      )
    }
  }

  # Add all network data
  if (exists("bacteria_alpine_old_network")) {
    add_network_data(bacteria_alpine_old_network, nrow(bacteria_alpine_otu), "Bacteria_Alpine_Old")
  }
  if (exists("bacteria_alpine_new_network")) {
    add_network_data(bacteria_alpine_new_network, nrow(bacteria_alpine_otu), "Bacteria_Alpine_New")
  }
  if (exists("bacteria_farming_old_network")) {
    add_network_data(bacteria_farming_old_network, nrow(bacteria_farming_otu), "Bacteria_Farming_Old")
  }
  if (exists("bacteria_farming_new_network")) {
    add_network_data(bacteria_farming_new_network, nrow(bacteria_farming_otu), "Bacteria_Farming_New")
  }
  if (exists("fungi_alpine_old_network")) {
    add_network_data(fungi_alpine_old_network, nrow(fungi_alpine_otu), "Fungi_Alpine_Old")
  }
  if (exists("fungi_alpine_new_network")) {
    add_network_data(fungi_alpine_new_network, nrow(fungi_alpine_otu), "Fungi_Alpine_New")
  }
  if (exists("fungi_farming_old_network")) {
    add_network_data(fungi_farming_old_network, nrow(fungi_farming_otu), "Fungi_Farming_Old")
  }
  if (exists("fungi_farming_new_network")) {
    add_network_data(fungi_farming_new_network, nrow(fungi_farming_otu), "Fungi_Farming_New")
  }



  # Check if we have any network data
  if (length(network_data) > 0) {
    # Create two data frames: one for raw metrics and one for normalized metrics

    # Create separate data frames for raw and normalized metrics
    # We'll keep them separate to avoid column mismatch issues

    # Raw metrics
    raw_networks <- data.frame(
      Group = names(network_data),
      Nodes = sapply(network_data, function(x) x$Nodes),
      Edges = sapply(network_data, function(x) x$Edges),
      Avg_Degree = sapply(network_data, function(x) x$Avg_Degree),
      Max_Degree = sapply(network_data, function(x) x$Max_Degree),
      Avg_Betweenness = sapply(network_data, function(x) x$Avg_Betweenness),
      Avg_Closeness = sapply(network_data, function(x) x$Avg_Closeness),
      Communities = sapply(network_data, function(x) x$Communities),
      Modularity = sapply(network_data, function(x) x$Modularity),
      Hub_Nodes = sapply(network_data, function(x) x$Hub_Nodes)
    )

    # Normalized metrics
    norm_networks <- data.frame(
      Group = names(network_data),
      Norm_Nodes = sapply(network_data, function(x) ifelse(is.null(x$Norm_Nodes), NA, x$Norm_Nodes)),
      Norm_Edges = sapply(network_data, function(x) ifelse(is.null(x$Norm_Edges), NA, x$Norm_Edges)),
      Norm_Avg_Degree = sapply(network_data, function(x) ifelse(is.null(x$Norm_Avg_Degree), NA, x$Norm_Avg_Degree)),
      Norm_Max_Degree = sapply(network_data, function(x) ifelse(is.null(x$Norm_Max_Degree), NA, x$Norm_Max_Degree)),
      Norm_Communities = sapply(network_data, function(x) ifelse(is.null(x$Norm_Communities), NA, x$Norm_Communities)),
      Network_Density = sapply(network_data, function(x) ifelse(is.null(x$Network_Density), NA, x$Network_Density))
    )

    # Save both to separate CSV files
    write.csv(raw_networks, "network_comparison_raw.csv", row.names = FALSE)
    write.csv(norm_networks, "network_comparison_normalized.csv", row.names = FALSE)
    cat("Network comparison saved to network_comparison_raw.csv and network_comparison_normalized.csv\n")

    # Create visualizations of network metrics

    # 1. Average Degree comparison (both raw and normalized)
    # Raw
    p1a <- ggplot(raw_networks, aes(x = Group, y = Avg_Degree)) +
      geom_bar(stat = "identity", fill = "steelblue") +
      theme_minimal() +
      ggtitle("Average Degree by Group (Raw)")
    ggsave("avg_degree_comparison_raw.pdf", p1a)
    p1b <- ggplot(norm_networks, aes(x = Group, y = Norm_Avg_Degree)) +
      geom_bar(stat = "identity", fill = "lightblue") +
      theme_minimal() +
      ggtitle("Average Degree by Group (Normalized)")
    ggsave("avg_degree_comparison_normalized.pdf", p1b)

    # 2. Network Size comparison (Nodes and Edges)
    # Raw
    network_size_raw <- data.frame(
      Group = rep(raw_networks$Group, 2),
      Metric = c(rep("Nodes", nrow(raw_networks)), rep("Edges", nrow(raw_networks))),
      Value = c(raw_networks$Nodes, raw_networks$Edges)
    )

    p2a <- ggplot(network_size_raw, aes(x = Group, y = Value, fill = Metric)) +
      geom_bar(stat = "identity", position = "dodge") +
      theme_minimal() +
      ggtitle("Network Size Comparison (Raw)") +
      scale_fill_manual(values = c("Nodes" = "darkgreen", "Edges" = "darkred"))
    ggsave("network_size_comparison_raw.pdf", p2a)

    # Normalized
    network_size_norm <- data.frame(
      Group = rep(norm_networks$Group, 2),
      Metric = c(rep("Norm_Nodes", nrow(norm_networks)), rep("Norm_Edges", nrow(norm_networks))),
      Value = c(norm_networks$Norm_Nodes, norm_networks$Norm_Edges)
    )

    p2b <- ggplot(network_size_norm, aes(x = Group, y = Value, fill = Metric)) +
      geom_bar(stat = "identity", position = "dodge") +
      theme_minimal() +
      ggtitle("Network Size Comparison (Normalized)") +
      scale_fill_manual(values = c("Norm_Nodes" = "lightgreen", "Norm_Edges" = "pink"))
    ggsave("network_size_comparison_normalized.pdf", p2b)

    # 3. Community Structure comparison
    # Raw
    community_data_raw <- data.frame(
      Group = raw_networks$Group,
      Communities = raw_networks$Communities,
      Modularity = raw_networks$Modularity
    )

    p3a <- ggplot(community_data_raw, aes(x = Group)) +
      geom_bar(aes(y = Communities), stat = "identity", fill = "purple") +
      geom_line(aes(y = Modularity * max(Communities) * 2, group = 1), color = "orange", size = 1.5) +
      geom_point(aes(y = Modularity * max(Communities) * 2), color = "orange", size = 3) +
      scale_y_continuous(name = "Number of Communities",
                         sec.axis = sec_axis(~./(max(community_data_raw$Communities) * 2), name = "Modularity")) +
      theme_minimal() +
      ggtitle("Community Structure Comparison (Raw)")
    ggsave("community_comparison_raw.pdf", p3a)

    # Normalized
    community_data_norm <- data.frame(
      Group = norm_networks$Group,
      Norm_Communities = norm_networks$Norm_Communities,
      Network_Density = norm_networks$Network_Density
    )

    p3b <- ggplot(community_data_norm, aes(x = Group)) +
      geom_bar(aes(y = Norm_Communities), stat = "identity", fill = "lavender") +
      geom_line(aes(y = Network_Density * max(Norm_Communities) * 2, group = 1), color = "red", size = 1.5) +
      geom_point(aes(y = Network_Density * max(Norm_Communities) * 2), color = "red", size = 3) +
      scale_y_continuous(name = "Normalized Communities",
                         sec.axis = sec_axis(~./(max(community_data_norm$Norm_Communities) * 2), name = "Network Density")) +
      theme_minimal() +
      ggtitle("Community Structure Comparison (Normalized)")
    ggsave("community_comparison_normalized.pdf", p3b)

    cat("Network visualizations saved to PDF files\n")

    # Add statistical tests to compare network properties
    cat("\n\n========== STATISTICAL TESTS ==========\n")

    # Add community-locality correlation analysis
    cat("\n========== COMMUNITY-LOCALITY CORRELATION ANALYSIS ==========\n")

    # Function to analyze community-locality associations
    analyze_community_locality <- function(network_obj, metadata_file, group_name) {
      tryCatch({
        # Check if network object exists and has community data
        if (is.null(network_obj) || !("community" %in% names(network_obj))) {
          cat("No community data available for", group_name, "\n")
          return(NULL)
        }

        # Try to read metadata
        metadata <- NULL
        if (file.exists(metadata_file)) {
          if (grepl("\\.xlsx$", metadata_file)) {
            metadata <- readxl::read_excel(metadata_file)
          } else if (grepl("\\.csv$", metadata_file)) {
            metadata <- read.csv(metadata_file)
          } else {
            cat("Unsupported metadata file format:", metadata_file, "\n")
            return(NULL)
          }
        } else {
          cat("Metadata file not found:", metadata_file, "\n")
          return(NULL)
        }

        if (is.null(metadata) || nrow(metadata) == 0) {
          cat("No metadata available for", group_name, "\n")
          return(NULL)
        }

        # Get community assignments
        community_data <- data.frame(
          OTU = V(network_obj$network)$name,
          Community = network_obj$community,
          stringsAsFactors = FALSE
        )

        # Get sample information from the network
        sample_data <- data.frame(
          Sample = colnames(network_obj$otu_table)[-1],  # Exclude first column (OTU IDs)
          stringsAsFactors = FALSE
        )

        # Merge with metadata to get locality information
        # First, find the column that contains sample IDs in metadata
        sample_col <- NULL
        for (col in colnames(metadata)) {
          if (any(sample_data$Sample %in% metadata[[col]])) {
            sample_col <- col
            break
          }
        }

        if (is.null(sample_col)) {
          cat("Could not find matching sample IDs in metadata for", group_name, "\n")
          return(NULL)
        }

        # Find locality column in metadata
        locality_col <- NULL
        possible_cols <- c("Locality", "Location", "Site", "SamplingLocation", "Sampling_Location", "Sampling_Site")
        for (col in possible_cols) {
          if (col %in% colnames(metadata)) {
            locality_col <- col
            break
          }
        }

        if (is.null(locality_col)) {
          cat("Could not find locality column in metadata for", group_name, "\n")
          return(NULL)
        }

        # Merge sample data with metadata
        merged_data <- merge(sample_data, metadata, by.x = "Sample", by.y = sample_col)

        # Count OTUs per community
        community_counts <- table(community_data$Community)

        # Get OTU presence per sample
        otu_presence <- network_obj$otu_table[, -1]  # Remove OTU ID column
        rownames(otu_presence) <- network_obj$otu_table[, 1]

        # Convert to presence/absence (1/0)
        otu_presence[otu_presence > 0] <- 1

        # For each community, calculate its prevalence in each locality
        communities <- unique(community_data$Community)
        localities <- unique(merged_data[[locality_col]])

        # Create a matrix to store community prevalence by locality
        community_by_locality <- matrix(0, nrow = length(communities), ncol = length(localities))
        rownames(community_by_locality) <- communities
        colnames(community_by_locality) <- localities

        # For each community
        for (comm in communities) {
          # Get OTUs in this community
          comm_otus <- community_data$OTU[community_data$Community == comm]

          # For each locality
          for (loc in localities) {
            # Get samples from this locality
            loc_samples <- merged_data$Sample[merged_data[[locality_col]] == loc]

            # Calculate the average presence of community OTUs in this locality
            if (length(loc_samples) > 0 && length(comm_otus) > 0) {
              # Get columns corresponding to these samples
              sample_cols <- match(loc_samples, colnames(otu_presence))
              sample_cols <- sample_cols[!is.na(sample_cols)]

              if (length(sample_cols) > 0) {
                # Get rows corresponding to community OTUs
                otu_rows <- match(comm_otus, rownames(otu_presence))
                otu_rows <- otu_rows[!is.na(otu_rows)]

                if (length(otu_rows) > 0) {
                  # Calculate average presence
                  presence_subset <- otu_presence[otu_rows, sample_cols, drop = FALSE]
                  avg_presence <- sum(presence_subset) / (length(otu_rows) * length(sample_cols))
                  community_by_locality[as.character(comm), loc] <- avg_presence
                }
              }
            }
          }
        }

        # Perform chi-square test for association between communities and localities
        # Convert the matrix to a data frame for analysis
        community_locality_df <- as.data.frame.table(community_by_locality)
        names(community_locality_df) <- c("Community", "Locality", "Prevalence")

        # Only keep non-zero entries
        community_locality_df <- community_locality_df[community_locality_df$Prevalence > 0, ]

        # Create a contingency table
        contingency_table <- xtabs(Prevalence ~ Community + Locality, data = community_locality_df)

        # Perform chi-square test
        chi_test <- chisq.test(contingency_table, simulate.p.value = TRUE)

        # Save the community-locality matrix
        write.csv(community_by_locality,
                  paste0("community_locality_", tolower(group_name), ".csv"))

        # Create a heatmap visualization
        pdf(paste0("community_locality_heatmap_", tolower(group_name), ".pdf"),
            width = max(8, length(localities) * 0.5),
            height = max(8, length(communities) * 0.3))

        # Normalize for better visualization
        normalized_matrix <- community_by_locality
        for (i in 1:nrow(normalized_matrix)) {
          row_max <- max(normalized_matrix[i, ])
          if (row_max > 0) {
            normalized_matrix[i, ] <- normalized_matrix[i, ] / row_max
          }
        }

        # Create heatmap
        heatmap(normalized_matrix,
                col = colorRampPalette(c("white", "yellow", "orange", "red"))(100),
                main = paste("Community-Locality Association for", group_name),
                xlab = "Locality",
                ylab = "Community")

        dev.off()

        # Return results
        return(list(
          chi_test = chi_test,
          community_by_locality = community_by_locality,
          community_locality_df = community_locality_df
        ))
      }, error = function(e) {
        cat("Error in community-locality analysis for", group_name, ":", e$message, "\n")
        return(NULL)
      })
    }

    # Analyze each network
    if (exists("fungi_network") && !is.null(fungi_network)) {
      cat("\nAnalyzing community-locality associations for Fungi\n")
      fungi_locality <- analyze_community_locality(fungi_network, "metadata_fungi.xlsx", "Fungi")
      if (!is.null(fungi_locality) && !is.null(fungi_locality$chi_test)) {
        cat("Chi-square test p-value:", fungi_locality$chi_test$p.value, "\n")
        cat("Significant association:", ifelse(fungi_locality$chi_test$p.value < 0.05, "Yes", "No"), "\n")
      }
    }

    if (exists("bacteria_network") && !is.null(bacteria_network)) {
      cat("\nAnalyzing community-locality associations for Bacteria\n")
      bacteria_locality <- analyze_community_locality(bacteria_network, "metadata_bacteria.xlsx", "Bacteria")
      if (!is.null(bacteria_locality) && !is.null(bacteria_locality$chi_test)) {
        cat("Chi-square test p-value:", bacteria_locality$chi_test$p.value, "\n")
        cat("Significant association:", ifelse(bacteria_locality$chi_test$p.value < 0.05, "Yes", "No"), "\n")
      }
    }

    if (exists("metazoa_network") && !is.null(metazoa_network)) {
      cat("\nAnalyzing community-locality associations for Metazoa\n")
      metazoa_locality <- analyze_community_locality(metazoa_network, "metadata_metazoa.xlsx", "Metazoa")
      if (!is.null(metazoa_locality) && !is.null(metazoa_locality$chi_test)) {
        cat("Chi-square test p-value:", metazoa_locality$chi_test$p.value, "\n")
        cat("Significant association:", ifelse(metazoa_locality$chi_test$p.value < 0.05, "Yes", "No"), "\n")
      }
    }

    # Add standard statistical tests to compare network properties
    cat("\n========== NETWORK PROPERTY COMPARISON TESTS ==========\n")

    # Only perform tests if we have at least 2 networks
    if (nrow(raw_networks) >= 2) {
      # Create a function to perform statistical tests on network properties
      perform_network_tests <- function() {
        # Create a data frame to store test results
        test_results <- data.frame(
          Comparison = character(),
          Property = character(),
          Test = character(),
          P_Value = numeric(),
          Significant = character(),
          stringsAsFactors = FALSE
        )

        # Get all pairwise combinations of networks
        network_pairs <- combn(raw_networks$Group, 2, simplify = FALSE)

        # Properties to test
        properties <- c("Avg_Degree", "Avg_Betweenness", "Avg_Closeness", "Modularity")

        # Perform tests for each pair and property
        for (pair in network_pairs) {
          group1 <- pair[1]
          group2 <- pair[2]
          comparison_name <- paste(group1, "vs", group2)

          # Get network objects
          network1 <- NULL
          network2 <- NULL

          if (group1 == "Fungi" && exists("fungi_network")) network1 <- fungi_network
          if (group1 == "Bacteria" && exists("bacteria_network")) network1 <- bacteria_network
          if (group1 == "Metazoa" && exists("metazoa_network")) network1 <- metazoa_network

          if (group2 == "Fungi" && exists("fungi_network")) network2 <- fungi_network
          if (group2 == "Bacteria" && exists("bacteria_network")) network2 <- bacteria_network
          if (group2 == "Metazoa" && exists("metazoa_network")) network2 <- metazoa_network

          if (!is.null(network1) && !is.null(network2)) {
            # Compare network properties
            cat("\nComparing", group1, "vs", group2, "\n")

            # 1. Compare degree distributions using Kolmogorov-Smirnov test
            tryCatch({
              ks_test <- ks.test(network1$degree, network2$degree)
              p_value <- ks_test$p.value
              significant <- ifelse(p_value < 0.05, "Yes", "No")

              test_results <- rbind(test_results, data.frame(
                Comparison = comparison_name,
                Property = "Degree Distribution",
                Test = "Kolmogorov-Smirnov",
                P_Value = p_value,
                Significant = significant,
                stringsAsFactors = FALSE
              ))

              cat("Degree Distribution (KS test): p-value =", p_value,
                  ifelse(significant == "Yes", "(Significant)", "(Not Significant)"), "\n")
            }, error = function(e) {
              cat("Error in KS test for degree distribution:", e$message, "\n")
            })

            # 2. Compare betweenness distributions
            tryCatch({
              ks_test <- ks.test(network1$betweenness, network2$betweenness)
              p_value <- ks_test$p.value
              significant <- ifelse(p_value < 0.05, "Yes", "No")

              test_results <- rbind(test_results, data.frame(
                Comparison = comparison_name,
                Property = "Betweenness Distribution",
                Test = "Kolmogorov-Smirnov",
                P_Value = p_value,
                Significant = significant,
                stringsAsFactors = FALSE
              ))

              cat("Betweenness Distribution (KS test): p-value =", p_value,
                  ifelse(significant == "Yes", "(Significant)", "(Not Significant)"), "\n")
            }, error = function(e) {
              cat("Error in KS test for betweenness distribution:", e$message, "\n")
            })

            # 3. Compare network density
            density1 <- edge_density(network1$network)
            density2 <- edge_density(network2$network)
            cat("Network Density:", group1, "=", density1, ",", group2, "=", density2, "\n")

            # 4. Compare modularity
            mod1 <- network1$modularity
            mod2 <- network2$modularity
            cat("Modularity:", group1, "=", mod1, ",", group2, "=", mod2, "\n")
          }
        }

        # Save test results to CSV
        write.csv(test_results, "network_statistical_tests.csv", row.names = FALSE)
        cat("Statistical test results saved to network_statistical_tests.csv\n")

        return(test_results)
      }

      # Run the tests
      test_results <- perform_network_tests()

      # Create visualization of test results
      if (nrow(test_results) > 0) {
        # Create a heatmap of p-values
        test_results$NegLog10P <- -log10(test_results$P_Value)

        p4 <- ggplot(test_results, aes(x = Comparison, y = Property, fill = NegLog10P)) +
          geom_tile() +
          scale_fill_gradient(low = "white", high = "red",
                             name = "-log10(p-value)",
                             guide = guide_colorbar(title.position = "top")) +
          geom_text(aes(label = ifelse(Significant == "Yes", "*", "")),
                    color = "black", size = 8) +
          theme_minimal() +
          theme(axis.text.x = element_text(angle = 45, hjust = 1)) +
          ggtitle("Statistical Significance of Network Property Differences")

        ggsave("network_statistical_tests.pdf", p4, width = 8, height = 6)
        cat("Statistical test visualization saved to network_statistical_tests.pdf\n")
      }
    } else {
      cat("Need at least 2 networks to perform statistical tests\n")
    }
  } else {
    cat("No network data available for comparison\n")
  }
}, error = function(e) {
  cat("Error in network comparison:", e$message, "\n")
})


############CORRELATION AMONG DIFFERENT GROUPS#####
#CREATE CORRELATION MATRIX
create_cross_correlation_matrix <- function(otu_table1, otu_table2, threshold = 0.6, max_otus1 = 2000, max_otus2 = 2000) {
  # Print initial dimensions
  cat("Initial dimensions:\n")
  cat("Table 1:", dim(otu_table1), "\n")
  cat("Table 2:", dim(otu_table2), "\n")

  # Extract OTU matrices (remove first column which is likely OTU IDs)
  otu_matrix1 <- as.matrix(otu_table1[,-1])
  otu_matrix2 <- as.matrix(otu_table2[,-1])

  # Check for rows with all zeros or constant values in both matrices
  row_sums1 <- rowSums(otu_matrix1)
  constant_rows1 <- row_sums1 == 0 | apply(otu_matrix1, 1, function(x) length(unique(x)) <= 1)
  if (any(constant_rows1)) {
    cat("Warning: Removing", sum(constant_rows1), "constant rows from table 1\n")
    otu_matrix1 <- otu_matrix1[!constant_rows1, , drop = FALSE]
  }

  row_sums2 <- rowSums(otu_matrix2)
  constant_rows2 <- row_sums2 == 0 | apply(otu_matrix2, 1, function(x) length(unique(x)) <= 1)
  if (any(constant_rows2)) {
    cat("Warning: Removing", sum(constant_rows2), "constant rows from table 2\n")
    otu_matrix2 <- otu_matrix2[!constant_rows2, , drop = FALSE]
  }

  # Memory management: Filter to most abundant/variable OTUs if datasets are too large
  if (nrow(otu_matrix1) > max_otus1) {
    cat("Table 1 too large (", nrow(otu_matrix1), " OTUs). Filtering to top", max_otus1, "most abundant OTUs\n")

    # Calculate abundance and variance for each OTU
    otu_abundance1 <- rowMeans(otu_matrix1)
    otu_variance1 <- apply(otu_matrix1, 1, var)

    # Create a combined score (abundance + variance, both normalized)
    abundance_norm1 <- (otu_abundance1 - min(otu_abundance1)) / (max(otu_abundance1) - min(otu_abundance1))
    variance_norm1 <- (otu_variance1 - min(otu_variance1)) / (max(otu_variance1) - min(otu_variance1))
    combined_score1 <- abundance_norm1 + variance_norm1

    # Select top OTUs based on combined score
    top_otus1 <- order(combined_score1, decreasing = TRUE)[1:max_otus1]
    otu_matrix1 <- otu_matrix1[top_otus1, , drop = FALSE]

    cat("Filtered table 1 to", nrow(otu_matrix1), "OTUs\n")
  }

  if (nrow(otu_matrix2) > max_otus2) {
    cat("Table 2 too large (", nrow(otu_matrix2), " OTUs). Filtering to top", max_otus2, "most abundant OTUs\n")

    # Calculate abundance and variance for each OTU
    otu_abundance2 <- rowMeans(otu_matrix2)
    otu_variance2 <- apply(otu_matrix2, 1, var)

    # Create a combined score (abundance + variance, both normalized)
    abundance_norm2 <- (otu_abundance2 - min(otu_abundance2)) / (max(otu_abundance2) - min(otu_abundance2))
    variance_norm2 <- (otu_variance2 - min(otu_variance2)) / (max(otu_variance2) - min(otu_variance2))
    combined_score2 <- abundance_norm2 + variance_norm2

    # Select top OTUs based on combined score
    top_otus2 <- order(combined_score2, decreasing = TRUE)[1:max_otus2]
    otu_matrix2 <- otu_matrix2[top_otus2, , drop = FALSE]

    cat("Filtered table 2 to", nrow(otu_matrix2), "OTUs\n")
  }

  # Get sample names (column names)
  samples1 <- colnames(otu_table1)[-1]  # Exclude first column
  samples2 <- colnames(otu_table2)[-1]  # Exclude first column

  # Find common samples
  common_samples <- intersect(samples1, samples2)
  cat("\nNumber of common samples:", length(common_samples), "\n")

  if (length(common_samples) == 0) {
    stop("No common samples found between the two OTU tables")
  }

  if (length(common_samples) < 3) {
    stop("Not enough common samples for correlation analysis (need at least 3)")
  }

  # Find indices of common samples in each table
  idx1 <- match(common_samples, samples1)
  idx2 <- match(common_samples, samples2)

  # Subset matrices to common samples
  matrix1 <- otu_matrix1[, idx1, drop = FALSE]
  matrix2 <- otu_matrix2[, idx2, drop = FALSE]

  # Print dimensions after filtering
  cat("\nAfter filtering:\n")
  cat("Matrix 1 dimensions:", dim(matrix1), "\n")
  cat("Matrix 2 dimensions:", dim(matrix2), "\n")

  # Calculate correlations between the two groups
  tryCatch({
    # Use pairwise.complete.obs to handle any NAs
    cor_matrix <- cor(t(matrix1), t(matrix2), method = "spearman", use = "pairwise.complete.obs")

    # Replace any NAs with 0
    if (any(is.na(cor_matrix))) {
      cat("Warning: Replacing", sum(is.na(cor_matrix)), "NA values with 0\n")
      cor_matrix[is.na(cor_matrix)] <- 0
    }

    # Print summary before thresholding
    cat("\nBefore thresholding:\n")
    cat("Total correlations:", length(cor_matrix), "\n")
    cat("Positive correlations:", sum(cor_matrix > 0), "\n")
    cat("Negative correlations:", sum(cor_matrix < 0), "\n")
    cat("Range:", range(cor_matrix), "\n")

    # Store signs before thresholding
    signs <- sign(cor_matrix)

    # Apply threshold to absolute values
    cor_matrix[abs(cor_matrix) < threshold] <- 0

    # Restore signs
    cor_matrix <- cor_matrix * signs

    # Print summary after thresholding
    cat("\nAfter thresholding (|correlation| ≥", threshold, "):\n")
    cat("Total non-zero correlations:", sum(cor_matrix != 0), "\n")
    cat("Positive correlations:", sum(cor_matrix > 0), "\n")
    cat("Negative correlations:", sum(cor_matrix < 0), "\n")

    if (sum(cor_matrix != 0) > 0) {
      cat("Range of non-zero correlations:", range(cor_matrix[cor_matrix != 0]), "\n")
    } else {
      cat("No correlations above threshold.\n")
    }

    # Return the correlation matrix and the filtered row indices
    return(list(
      cor_matrix = cor_matrix,
      filtered_indices1 = which(!constant_rows1),
      filtered_indices2 = which(!constant_rows2)
    ))
  }, error = function(e) {
    cat("Error in correlation calculation:", e$message, "\n")
    # Return an empty matrix as fallback
    return(list(
      cor_matrix = matrix(0, nrow = nrow(matrix1), ncol = nrow(matrix2)),
      filtered_indices1 = integer(0),
      filtered_indices2 = integer(0)
    ))
  })
}

#Analyse the correlation
analyze_cross_network <- function(cor_result, tax1, tax2, group1_name, group2_name) {
  # Extract correlation matrix from result
  if (is.list(cor_result) && "cor_matrix" %in% names(cor_result)) {
    cor_matrix <- cor_result$cor_matrix
    filtered_indices1 <- cor_result$filtered_indices1
    filtered_indices2 <- cor_result$filtered_indices2
  } else {
    # For backward compatibility
    cor_matrix <- cor_result
  }
  # Get non-zero correlations
  significant_cors <- which(cor_matrix != 0, arr.ind = TRUE)

  # Check if there are any significant correlations
  if (length(significant_cors) == 0 || nrow(significant_cors) == 0) {
    cat("\nNo significant correlations found between", group1_name, "and", group2_name, "\n")
    return(NULL)
  }

  weights <- cor_matrix[significant_cors]

  # Create edge list
  edge_list <- data.frame(
    from = significant_cors[,1],
    to = significant_cors[,2] + nrow(cor_matrix),  # Offset second group indices
    weight = weights
  )

  # Create vertex list with types
  vertices <- data.frame(
    name = c(1:nrow(cor_matrix), (nrow(cor_matrix)+1):(nrow(cor_matrix)+ncol(cor_matrix))),
    type = c(rep(group1_name, nrow(cor_matrix)),
             rep(group2_name, ncol(cor_matrix)))
  )

  # Create network
  tryCatch({
    network <- graph_from_data_frame(
      d = edge_list,
      vertices = vertices,
      directed = FALSE
    )

    # Network analysis results
    cat("\nCross-Network Analysis Results:", group1_name, "vs", group2_name, "\n")
    cat("Number of nodes from", group1_name, ":", sum(V(network)$type == group1_name), "\n")
    cat("Number of nodes from", group2_name, ":", sum(V(network)$type == group2_name), "\n")
    cat("Number of edges:", ecount(network), "\n")

    # Create network plot
    plot_title <- paste("Network between", group1_name, "and", group2_name)
    pdf(paste0("cross_network_", tolower(group1_name), "_", tolower(group2_name), ".pdf"))

    # Set node colors by group
    V(network)$color <- ifelse(V(network)$type == group1_name, "lightblue", "lightgreen")

    # Calculate node degrees for sizing
    node_degrees <- degree(network)

    # Scale node sizes based on degree (more connections = larger node)
    min_size <- 2
    max_size <- 10
    if (max(node_degrees) > min(node_degrees)) {
      node_sizes <- min_size + (max_size - min_size) *
                  (node_degrees - min(node_degrees)) / (max(node_degrees) - min(node_degrees))
    } else {
      node_sizes <- rep(min_size, length(node_degrees))
    }

    # Store node degrees for later use
    V(network)$degree <- node_degrees
    V(network)$size <- node_sizes

    # Edge properties
    E(network)$color <- ifelse(E(network)$weight > 0, "darkblue", "darkred")
    E(network)$width <- abs(E(network)$weight) * 2

    # Store absolute weight for potential filtering
    E(network)$abs_weight <- abs(E(network)$weight)

    plot(network,
         vertex.size = node_sizes,
         vertex.label = NA,
         edge.width = E(network)$width,
         edge.color = E(network)$color,
         main = plot_title,
         layout = layout_with_fr(network))

    # Add legend
    legend("bottomright",
           legend = c(group1_name, group2_name, "Positive correlation", "Hub node", "Regular node"),
           fill = c("lightblue", "lightgreen", "darkblue", NA, NA),
           pch = c(NA, NA, NA, 19, 19),
           pt.cex = c(NA, NA, NA, 2, 1),
           cex = 0.8)

    dev.off()

    # Export hub nodes with degree > 6 to a separate file
    # Find nodes with degree greater than 6
    hub_nodes <- which(node_degrees > 6)
    # Sort hub nodes by degree in descending order
    hub_nodes <- hub_nodes[order(node_degrees[hub_nodes], decreasing = TRUE)]
    cat("Selected", length(hub_nodes), "hub nodes with degree > 6\n")

    if (length(hub_nodes) > 0) {
      # Create a data frame to store hub node information
      hub_data <- data.frame(
        Node_ID = hub_nodes,
        Group = V(network)$type[hub_nodes],
        Degree = node_degrees[hub_nodes],
        OTU_ID = NA,
        stringsAsFactors = FALSE
      )

      # Add OTU ID information if available
      # For group 1 nodes
      group1_nodes <- hub_nodes[V(network)$type[hub_nodes] == group1_name]
      if (length(group1_nodes) > 0 && !is.null(tax1) && nrow(tax1) > 0) {
        # Map network node IDs back to original indices
        if (is.list(cor_result) && "filtered_indices1" %in% names(cor_result)) {
          filtered_indices1 <- cor_result$filtered_indices1

          for (i in seq_along(group1_nodes)) {
            node_id <- group1_nodes[i]
            # Only process nodes from the first group
            if (node_id <= length(filtered_indices1)) {
              original_idx <- filtered_indices1[node_id]

              # Check if index is within bounds
              if (original_idx <= nrow(tax1)) {
                # Add OTU ID (first column of taxonomy data)
                hub_data$OTU_ID[hub_data$Node_ID == node_id] <- as.character(tax1[original_idx, 1])
              }
            }
          }
        }
      }

      # For group 2 nodes
      group2_nodes <- hub_nodes[V(network)$type[hub_nodes] == group2_name]
      if (length(group2_nodes) > 0 && !is.null(tax2) && nrow(tax2) > 0) {
        # Map network node IDs back to original indices
        if (is.list(cor_result) && "filtered_indices2" %in% names(cor_result)) {
          filtered_indices2 <- cor_result$filtered_indices2

          for (i in seq_along(group2_nodes)) {
            node_id <- group2_nodes[i]
            # Adjust node_id to get the correct index in the second group
            adjusted_id <- node_id - length(filtered_indices1)

            if (adjusted_id > 0 && adjusted_id <= length(filtered_indices2)) {
              original_idx <- filtered_indices2[adjusted_id]

              # Check if index is within bounds
              if (original_idx <= nrow(tax2)) {
                # Add OTU ID (first column of taxonomy data)
                hub_data$OTU_ID[hub_data$Node_ID == node_id] <- as.character(tax2[original_idx, 1])
              }
            }
          }
        }
      }

      # Sort hub nodes by degree (descending)
      hub_data <- hub_data[order(hub_data$Degree, decreasing = TRUE), ]

      # Save hub node information to a CSV file
      hub_file <- paste0("hub_nodes_cross_", tolower(group1_name), "_", tolower(group2_name), ".csv")
      write.csv(hub_data, hub_file, row.names = FALSE)
      cat("Hub node information saved to", hub_file, "\n")
    }

    # Calculate additional network metrics
    betweenness_values <- betweenness(network, normalized = TRUE)
    closeness_values <- closeness(network, normalized = TRUE)

    # Calculate community structure
    cat("\nDetecting communities in cross-network...\n")
    communities <- NULL
    best_modularity <- -1
    best_algorithm <- "none"

    # Try Louvain algorithm (works well for most networks)
    tryCatch({
      cat("Trying Louvain algorithm...\n")
      louvain_comm <- cluster_louvain(network, weights = abs(E(network)$weight))
      louvain_mod <- modularity(network, membership(louvain_comm))
      cat("Louvain modularity:", louvain_mod, "\n")

      if (louvain_mod > best_modularity) {
        best_modularity <- louvain_mod
        communities <- louvain_comm
        best_algorithm <- "Louvain"
      }
    }, error = function(e) {
      cat("Error in Louvain algorithm:", e$message, "\n")
    })

    # Try Walktrap algorithm
    tryCatch({
      cat("Trying Walktrap algorithm...\n")
      walktrap_comm <- cluster_walktrap(network, weights = abs(E(network)$weight))
      walktrap_mod <- modularity(network, membership(walktrap_comm))
      cat("Walktrap modularity:", walktrap_mod, "\n")

      if (walktrap_mod > best_modularity) {
        best_modularity <- walktrap_mod
        communities <- walktrap_comm
        best_algorithm <- "Walktrap"
      }
    }, error = function(e) {
      cat("Error in Walktrap algorithm:", e$message, "\n")
    })

    # If all algorithms failed, create a fallback community structure
    if (is.null(communities)) {
      cat("All community detection algorithms failed. Creating fallback community structure.\n")
      communities <- make_clusters(network, membership = rep(1, vcount(network)))
      best_algorithm <- "Fallback (single community)"
      best_modularity <- 0
    } else {
      cat("Selected", best_algorithm, "algorithm with modularity", best_modularity, "\n")
    }

    # Get community membership
    comm_membership <- membership(communities)
    community_count <- length(unique(comm_membership))
    cat("Number of communities:", community_count, "\n")

    # Store community membership in network
    V(network)$community <- comm_membership

    # Create community data file
    community_data <- data.frame(
      Node_ID = 1:vcount(network),
      Group = V(network)$type,
      Community = comm_membership,
      Degree = node_degrees,
      Betweenness = betweenness_values,
      OTU_ID = NA,
      stringsAsFactors = FALSE
    )

    # Add taxonomy information if available
    # For group 1 nodes
    group1_nodes <- which(V(network)$type == group1_name)
    if (length(group1_nodes) > 0 && !is.null(tax1) && nrow(tax1) > 0) {
      # Map network node IDs back to original indices
      if (is.list(cor_result) && "filtered_indices1" %in% names(cor_result)) {
        filtered_indices1 <- cor_result$filtered_indices1

        for (i in seq_along(group1_nodes)) {
          node_id <- group1_nodes[i]
          # Only process nodes from the first group
          if (node_id <= length(filtered_indices1)) {
            original_idx <- filtered_indices1[node_id]

            # Check if index is within bounds
            if (original_idx <= nrow(tax1)) {
              # Add OTU ID (first column of taxonomy data)
              community_data$OTU_ID[community_data$Node_ID == node_id] <- as.character(tax1[original_idx, 1])
            }
          }
        }
      }
    }

    # For group 2 nodes
    group2_nodes <- which(V(network)$type == group2_name)
    if (length(group2_nodes) > 0 && !is.null(tax2) && nrow(tax2) > 0) {
      # Map network node IDs back to original indices
      if (is.list(cor_result) && "filtered_indices2" %in% names(cor_result)) {
        filtered_indices2 <- cor_result$filtered_indices2

        for (i in seq_along(group2_nodes)) {
          node_id <- group2_nodes[i]
          # Adjust node_id to get the correct index in the second group
          adjusted_id <- node_id - nrow(cor_matrix)

          if (adjusted_id > 0 && adjusted_id <= length(filtered_indices2)) {
            original_idx <- filtered_indices2[adjusted_id]

            # Check if index is within bounds
            if (original_idx <= nrow(tax2)) {
              # Add OTU ID (first column of taxonomy data)
              community_data$OTU_ID[community_data$Node_ID == node_id] <- as.character(tax2[original_idx, 1])
            }
          }
        }
      }
    }

    # Sort by community and then by degree (descending) within each community
    community_data <- community_data[order(community_data$Community, -community_data$Degree), ]

    # Save community information to a CSV file
    community_file <- paste0("community_data_cross_", tolower(group1_name), "_", tolower(group2_name), ".csv")
    write.csv(community_data, community_file, row.names = FALSE)
    cat("Community information saved to", community_file, "\n")

    # Create a filtered visualization for networks involving bacteria
    if (group1_name == "Bacteria" || group2_name == "Bacteria" || vcount(network) > 1000) {
      cat("\nCreating filtered visualization for", group1_name, "vs", group2_name, "network...\n")

      # Create a filtered network based on node degree and edge weight
      # For bacteria networks, we'll filter more aggressively
      degree_threshold <- 5
      weight_threshold <- 0.65

      # If bacteria is involved, use more aggressive filtering
      if (group1_name == "Bacteria" || group2_name == "Bacteria") {
        degree_threshold <- 8
        weight_threshold <- 0.7
      }

      cat("Filtering nodes with degree <", degree_threshold, "and edges with weight <", weight_threshold, "\n")

      # Create a copy of the network for filtering
      filtered_network <- network

      # First, identify nodes to keep (those with high degree)
      high_degree_nodes <- which(node_degrees >= degree_threshold)

      # If we have too few nodes after filtering, adjust the threshold
      if (length(high_degree_nodes) < 50) {
        # Find a threshold that keeps at least 50 nodes or 10% of the original nodes
        min_nodes <- min(50, ceiling(vcount(network) * 0.1))
        sorted_degrees <- sort(node_degrees, decreasing = TRUE)
        if (length(sorted_degrees) >= min_nodes) {
          degree_threshold <- sorted_degrees[min_nodes]
          high_degree_nodes <- which(node_degrees >= degree_threshold)
          cat("Adjusted degree threshold to", degree_threshold, "to keep at least", min_nodes, "nodes\n")
        }
      }

      # Create a subgraph with only the high-degree nodes
      if (length(high_degree_nodes) > 0) {
        filtered_network <- induced_subgraph(network, high_degree_nodes)

        # Further filter edges by weight
        edges_to_remove <- which(E(filtered_network)$abs_weight < weight_threshold)
        if (length(edges_to_remove) > 0) {
          filtered_network <- delete_edges(filtered_network, edges_to_remove)
        }

        # Remove isolated nodes (degree 0 after edge filtering)
        isolated_nodes <- which(degree(filtered_network) == 0)
        if (length(isolated_nodes) > 0) {
          filtered_network <- delete_vertices(filtered_network, isolated_nodes)
        }

        # Only proceed if we have nodes and edges left
        if (vcount(filtered_network) > 0 && ecount(filtered_network) > 0) {
          # Create a PDF for the filtered network
          pdf(paste0("cross_network_", tolower(group1_name), "_", tolower(group2_name), "_filtered.pdf"))

          # Recalculate node properties for the filtered network
          filtered_node_degrees <- degree(filtered_network)

          # Scale node sizes based on degree
          min_size <- 3  # Slightly larger minimum size for better visibility
          max_size <- 12
          if (max(filtered_node_degrees) > min(filtered_node_degrees)) {
            filtered_node_sizes <- min_size + (max_size - min_size) *
                            (filtered_node_degrees - min(filtered_node_degrees)) /
                            (max(filtered_node_degrees) - min(filtered_node_degrees))
          } else {
            filtered_node_sizes <- rep(min_size, length(filtered_node_degrees))
          }

          # Set node colors by group (same as original)
          V(filtered_network)$color <- ifelse(V(filtered_network)$type == group1_name, "lightblue", "lightgreen")

          # Edge properties
          filtered_edge_colors <- ifelse(E(filtered_network)$weight > 0, "darkblue", "darkred")
          filtered_edge_widths <- abs(E(filtered_network)$weight) * 3  # Slightly thicker for better visibility

          # Create a layout with longer edges to spread out nodes
          # Use Fruchterman-Reingold layout with increased area parameter
          fr_layout <- layout_with_fr(filtered_network,
                                     area = vcount(filtered_network)^2.5, # Increase area to spread nodes
                                     repulserad = vcount(filtered_network)^3, # Increase repulsion radius
                                     coolexp = 1.5) # Slower cooling for better layout

          # Plot the filtered network - standard layout
          plot(filtered_network,
               vertex.size = filtered_node_sizes,
               vertex.label = NA,
               vertex.color = V(filtered_network)$color,
               edge.color = filtered_edge_colors,
               edge.width = filtered_edge_widths,
               layout = layout_with_fr(filtered_network),
               main = paste("Network between", group1_name, "and", group2_name, "(filtered view)"))

          # Plot the filtered network - expanded layout
          plot(filtered_network,
               vertex.size = filtered_node_sizes,
               vertex.label = NA,
               vertex.color = V(filtered_network)$color,
               edge.color = filtered_edge_colors,
               edge.width = filtered_edge_widths,
               layout = fr_layout,
               main = paste("Network between", group1_name, "and", group2_name, "(filtered view, expanded layout)"))

          # If the filtered network has community structure, color by community
          if ("community" %in% vertex_attr_names(filtered_network)) {
            # Get community membership
            filtered_comm_membership <- V(filtered_network)$community

            # Get unique communities
            unique_communities <- sort(unique(filtered_comm_membership))
            filtered_community_count <- length(unique_communities)

            # Count nodes from each group in each community
            group_counts <- data.frame(
              Community = unique_communities,
              Group1_Count = sapply(unique_communities, function(comm) {
                sum(V(filtered_network)$type[filtered_comm_membership == comm] == group1_name)
              }),
              Group2_Count = sapply(unique_communities, function(comm) {
                sum(V(filtered_network)$type[filtered_comm_membership == comm] == group2_name)
              })
            )

            # Calculate total nodes in each community
            group_counts$Total <- group_counts$Group1_Count + group_counts$Group2_Count

            # Sort by total nodes (descending)
            group_counts <- group_counts[order(-group_counts$Total), ]

            # Print community composition
            cat("\nCommunity composition in filtered network:\n")
            print(group_counts)

            # Save community composition to CSV
            write.csv(group_counts,
                      paste0("community_composition_filtered_",
                             tolower(group1_name), "_",
                             tolower(group2_name), ".csv"),
                      row.names = FALSE)

            # Add legend for groups
            legend("bottomright",
                   legend = c(group1_name, group2_name, "Positive correlation"),
                   fill = c("lightblue", "lightgreen", NA),
                   col = c(NA, NA, "darkblue"),
                   lty = c(NA, NA, 1),
                   cex = 0.8)
          }

          # Close the PDF
          dev.off()

          cat("Filtered cross-network visualization saved to cross_network_",
              tolower(group1_name), "_", tolower(group2_name), "_filtered.pdf\n", sep="")

          # Save filtered network node information
          filtered_nodes_file <- paste0("filtered_nodes_cross_",
                                       tolower(group1_name), "_",
                                       tolower(group2_name), ".csv")

          # Create data frame with node information
          filtered_nodes_data <- data.frame(
            Node_ID = as.numeric(names(V(filtered_network))),
            Group = V(filtered_network)$type,
            Degree = filtered_node_degrees,
            Original_Node_ID = V(filtered_network)$name,
            stringsAsFactors = FALSE
          )

          write.csv(filtered_nodes_data, filtered_nodes_file, row.names = FALSE)
          cat("Filtered cross-network node information saved to", filtered_nodes_file, "\n")
        } else {
          cat("After filtering, no nodes or edges remain. Skipping filtered visualization.\n")
        }
      } else {
        cat("No nodes meet the filtering criteria. Skipping filtered visualization.\n")
      }
    }

    # Return enhanced network analysis results
    return(list(
      network = network,
      edge_list = edge_list,
      vertices = vertices,
      node_degrees = node_degrees,
      betweenness = betweenness_values,
      closeness = closeness_values,
      hub_nodes = hub_nodes,
      communities = communities,
      community_count = community_count,
      modularity = best_modularity,
      otu_table1 = cor_result$filtered_indices1,
      otu_table2 = cor_result$filtered_indices2,
      group1_name = group1_name,
      group2_name = group2_name
    ))
  }, error = function(e) {
    cat("Error creating cross-network:", e$message, "\n")
    return(NULL)
  })
}


# Compute correlations between different groups (Bacteria vs Fungi for each environment)
cat("\n\n========== CROSS-GROUP CORRELATIONS ==========\n")

# Helper function to perform cross-correlation analysis with multiple thresholds
perform_cross_correlation <- function(otu_table1, otu_table2, tax1, tax2, group1_name, group2_name) {
  # Determine max_otus based on group types
  max_otus1 <- if(grepl("Bacteria", group1_name)) 1500 else 3000
  max_otus2 <- if(grepl("Bacteria", group2_name)) 1500 else 3000

  # Try with threshold 0.6 first
  cat("\n\n----- ", group1_name, " vs ", group2_name, " -----\n", sep="")
  tryCatch({
    cross_cor <- create_cross_correlation_matrix(
      otu_table1,
      otu_table2,
      threshold = 0.6,
      max_otus1 = max_otus1,
      max_otus2 = max_otus2
    )

    # Check if we have any correlations at 0.6
    if (is.list(cross_cor)) {
      non_zero_cors <- sum(cross_cor$cor_matrix != 0)
    } else {
      non_zero_cors <- sum(cross_cor != 0)
    }

    if (non_zero_cors > 0) {
      cat("\nFound", non_zero_cors, "correlations above threshold 0.6 between", group1_name, "and", group2_name, ".\n")
      # Create and analyze the cross-group network
      cross_network <- analyze_cross_network(
        cross_cor,
        tax1,
        tax2,
        group1_name,
        group2_name
      )
      return(cross_network)
    } else {
      cat("\nNo correlations above threshold 0.6 found between", group1_name, "and", group2_name, ".\n")

      # Try with a lower threshold of 0.5
      cat("\n----- Trying", group1_name, "vs", group2_name, "with threshold 0.5 -----\n")
      cross_cor_05 <- create_cross_correlation_matrix(
        otu_table1,
        otu_table2,
        threshold = 0.5,
        max_otus1 = max_otus1,
        max_otus2 = max_otus2
      )

      if (is.list(cross_cor_05)) {
        non_zero_cors_05 <- sum(cross_cor_05$cor_matrix != 0)
      } else {
        non_zero_cors_05 <- sum(cross_cor_05 != 0)
      }

      if (non_zero_cors_05 > 0) {
        cat("\nFound", non_zero_cors_05, "correlations above threshold 0.5 between", group1_name, "and", group2_name, ".\n")
        cross_network <- analyze_cross_network(
          cross_cor_05,
          tax1,
          tax2,
          group1_name,
          paste(group2_name, "(threshold 0.5)")
        )
        return(cross_network)
      } else {
        cat("\nNo correlations above threshold 0.5 found between", group1_name, "and", group2_name, ".\n")

        # Try with an even lower threshold of 0.4
        cat("\n----- Trying", group1_name, "vs", group2_name, "with threshold 0.4 -----\n")
        cross_cor_04 <- create_cross_correlation_matrix(
          otu_table1,
          otu_table2,
          threshold = 0.4,
          max_otus1 = max_otus1,
          max_otus2 = max_otus2
        )

        if (is.list(cross_cor_04)) {
          non_zero_cors_04 <- sum(cross_cor_04$cor_matrix != 0)
        } else {
          non_zero_cors_04 <- sum(cross_cor_04 != 0)
        }

        if (non_zero_cors_04 > 0) {
          cat("\nFound", non_zero_cors_04, "correlations above threshold 0.4 between", group1_name, "and", group2_name, ".\n")
          cross_network <- analyze_cross_network(
            cross_cor_04,
            tax1,
            tax2,
            group1_name,
            paste(group2_name, "(threshold 0.4)")
          )
          return(cross_network)
        } else {
          cat("\nNo correlations above threshold 0.4 found between", group1_name, "and", group2_name, ".\n")
          cat("You might want to check the OTU tables for potential issues.\n")
          return(NULL)
        }
      }
    }
    cat(group1_name, "vs", group2_name, "cross-correlation completed successfully\n")
  }, error = function(e) {
    cat("Error in", group1_name, "vs", group2_name, "cross-correlation:", e$message, "\n")
    return(NULL)
  })
}

# Perform the 4 cross-correlations for bacteria vs fungi in each environment/time combination

# 1. Bacteria Alpine Old vs Fungi Alpine Old
if (!is.null(bacteria_alpine_old) && !is.null(fungi_alpine_old)) {
  bacteria_fungi_alpine_old_network <- perform_cross_correlation(
    bacteria_alpine_old, fungi_alpine_old,
    bacteria_alpine_tax, fungi_alpine_tax,
    "Bacteria_Alpine_Old", "Fungi_Alpine_Old"
  )
}

# 2. Bacteria Alpine New vs Fungi Alpine New
if (!is.null(bacteria_alpine_new) && !is.null(fungi_alpine_new)) {
  bacteria_fungi_alpine_new_network <- perform_cross_correlation(
    bacteria_alpine_new, fungi_alpine_new,
    bacteria_alpine_tax, fungi_alpine_tax,
    "Bacteria_Alpine_New", "Fungi_Alpine_New"
  )
}

# 3. Bacteria Farming Old vs Fungi Farming Old
if (!is.null(bacteria_farming_old) && !is.null(fungi_farming_old)) {
  bacteria_fungi_farming_old_network <- perform_cross_correlation(
    bacteria_farming_old, fungi_farming_old,
    bacteria_farming_tax, fungi_farming_tax,
    "Bacteria_Farming_Old", "Fungi_Farming_Old"
  )
}

# 4. Bacteria Farming New vs Fungi Farming New
if (!is.null(bacteria_farming_new) && !is.null(fungi_farming_new)) {
  bacteria_fungi_farming_new_network <- perform_cross_correlation(
    bacteria_farming_new, fungi_farming_new,
    bacteria_farming_tax, fungi_farming_tax,
    "Bacteria_Farming_New", "Fungi_Farming_New"
  )
}



# Analyze cross-correlation networks
cat("\n\n========== CROSS-CORRELATION NETWORK ANALYSIS ==========\n")

# Create a list to store cross-network data
cross_network_data <- list()

# Helper function to add cross-network data
add_cross_network_data <- function(network_obj, otu_count1, otu_count2, group1_name, group2_name, network_name) {
  if (exists(deparse(substitute(network_obj))) && !is.null(network_obj)) {
    # Calculate normalized metrics
    nodes_count <- vcount(network_obj$network)
    edges_count <- ecount(network_obj$network)

    # Count nodes from each group
    group1_nodes <- sum(V(network_obj$network)$type == group1_name)
    group2_nodes <- sum(V(network_obj$network)$type == group2_name)

    cross_network_data[[network_name]] <<- list(
      # Raw metrics
      Nodes = nodes_count,
      Group1_Nodes = group1_nodes,
      Group2_Nodes = group2_nodes,
      Edges = edges_count,
      Avg_Degree = mean(network_obj$node_degrees),
      Max_Degree = max(network_obj$node_degrees),
      Avg_Betweenness = mean(network_obj$betweenness),
      Avg_Closeness = mean(network_obj$closeness, na.rm = TRUE),
      Communities = network_obj$community_count,
      Modularity = network_obj$modularity,
      Hub_Nodes = length(network_obj$hub_nodes),

      # Normalized metrics
      Norm_Group1_Nodes = group1_nodes / otu_count1,
      Norm_Group2_Nodes = group2_nodes / otu_count2,
      Norm_Edges = edges_count / (group1_nodes * group2_nodes),
      Network_Density = edge_density(network_obj$network)
    )
  }
}

# Add all cross-network data using the helper function
if (exists("bacteria_fungi_alpine_old_network")) {
  add_cross_network_data(bacteria_fungi_alpine_old_network,
                        nrow(bacteria_alpine_otu), nrow(fungi_alpine_otu),
                        "Bacteria_Alpine_Old", "Fungi_Alpine_Old", "Bacteria_Fungi_Alpine_Old")
}

if (exists("bacteria_fungi_alpine_new_network")) {
  add_cross_network_data(bacteria_fungi_alpine_new_network,
                        nrow(bacteria_alpine_otu), nrow(fungi_alpine_otu),
                        "Bacteria_Alpine_New", "Fungi_Alpine_New", "Bacteria_Fungi_Alpine_New")
}

if (exists("bacteria_fungi_farming_old_network")) {
  add_cross_network_data(bacteria_fungi_farming_old_network,
                        nrow(bacteria_farming_otu), nrow(fungi_farming_otu),
                        "Bacteria_Farming_Old", "Fungi_Farming_Old", "Bacteria_Fungi_Farming_Old")
}

if (exists("bacteria_fungi_farming_new_network")) {
  add_cross_network_data(bacteria_fungi_farming_new_network,
                        nrow(bacteria_farming_otu), nrow(fungi_farming_otu),
                        "Bacteria_Farming_New", "Fungi_Farming_New", "Bacteria_Fungi_Farming_New")
}

# Check if we have any cross-network data
if (length(cross_network_data) > 0) {
  # Create data frames for cross-network comparison

  # Raw metrics
  cross_raw_metrics <- data.frame(
    Network = names(cross_network_data),
    Nodes = sapply(cross_network_data, function(x) x$Nodes),
    Edges = sapply(cross_network_data, function(x) x$Edges),
    Avg_Degree = sapply(cross_network_data, function(x) x$Avg_Degree),
    Max_Degree = sapply(cross_network_data, function(x) x$Max_Degree),
    Avg_Betweenness = sapply(cross_network_data, function(x) x$Avg_Betweenness),
    Avg_Closeness = sapply(cross_network_data, function(x) ifelse(is.null(x$Avg_Closeness), NA, x$Avg_Closeness)),
    Communities = sapply(cross_network_data, function(x) x$Communities),
    Modularity = sapply(cross_network_data, function(x) x$Modularity),
    Hub_Nodes = sapply(cross_network_data, function(x) x$Hub_Nodes)
  )

  # Normalized metrics
  cross_norm_metrics <- data.frame(
    Network = names(cross_network_data),
    Network_Density = sapply(cross_network_data, function(x) x$Network_Density),
    Norm_Edges = sapply(cross_network_data, function(x) x$Norm_Edges)
  )

  # Save comparison results
  write.csv(cross_raw_metrics, "cross_network_comparison_raw.csv", row.names = FALSE)
  write.csv(cross_norm_metrics, "cross_network_comparison_normalized.csv", row.names = FALSE)
  cat("Cross-network comparison saved to cross_network_comparison_raw.csv and cross_network_comparison_normalized.csv\n")

  # Create visualizations of cross-network metrics
  # 1. Network Size comparison
  p1 <- ggplot(cross_raw_metrics, aes(x = Network, y = Nodes)) +
    geom_bar(stat = "identity", fill = "steelblue") +
    theme_minimal() +
    ggtitle("Cross-Network Size Comparison")
  ggsave("cross_network_size_comparison.pdf", p1)

  # 2. Edge Count comparison
  p2 <- ggplot(cross_raw_metrics, aes(x = Network, y = Edges)) +
    geom_bar(stat = "identity", fill = "darkred") +
    theme_minimal() +
    ggtitle("Cross-Network Edge Count Comparison")
  ggsave("cross_network_edge_comparison.pdf", p2)

  # 3. Community Structure comparison
  p3 <- ggplot(cross_raw_metrics, aes(x = Network)) +
    geom_bar(aes(y = Communities), stat = "identity", fill = "purple") +
    geom_line(aes(y = Modularity * max(Communities) * 2, group = 1), color = "orange", size = 1.5) +
    geom_point(aes(y = Modularity * max(Communities) * 2), color = "orange", size = 3) +
    scale_y_continuous(name = "Number of Communities",
                       sec.axis = sec_axis(~./(max(cross_raw_metrics$Communities) * 2), name = "Modularity")) +
    theme_minimal() +
    ggtitle("Cross-Network Community Structure Comparison")
  ggsave("cross_network_community_comparison.pdf", p3)

  # 4. Network Density comparison
  p4 <- ggplot(cross_norm_metrics, aes(x = Network, y = Network_Density)) +
    geom_bar(stat = "identity", fill = "lightgreen") +
    theme_minimal() +
    ggtitle("Cross-Network Density Comparison")
  ggsave("cross_network_density_comparison.pdf", p4)

  cat("Cross-network visualizations saved to PDF files\n")

  # Community-locality correlation analysis for cross-networks has been removed as requested

  # Statistical tests for cross-networks
  cat("\n========== CROSS-NETWORK STATISTICAL TESTS ==========\n")

  # Only perform tests if we have at least 2 cross-networks
  if (nrow(cross_raw_metrics) >= 2) {
    # Create a function to perform statistical tests on cross-network properties
    perform_cross_network_tests <- function() {
      # Create a data frame to store test results
      test_results <- data.frame(
        Comparison = character(),
        Property = character(),
        Test = character(),
        P_Value = numeric(),
        Significant = character(),
        stringsAsFactors = FALSE
      )

      # Get all pairwise combinations of networks
      network_pairs <- combn(cross_raw_metrics$Network, 2, simplify = FALSE)

      # Properties to test
      properties <- c("Avg_Degree", "Avg_Betweenness", "Modularity", "Network_Density")

      # Perform tests for each pair and property
      for (pair in network_pairs) {
        network1 <- pair[1]
        network2 <- pair[2]
        comparison_name <- paste(network1, "vs", network2)

        # Get network objects
        network1_obj <- NULL
        network2_obj <- NULL

        if (network1 == "Bacteria_Fungi_Alpine_Old" && exists("bacteria_fungi_alpine_old_network")) network1_obj <- bacteria_fungi_alpine_old_network
        if (network1 == "Bacteria_Fungi_Alpine_New" && exists("bacteria_fungi_alpine_new_network")) network1_obj <- bacteria_fungi_alpine_new_network
        if (network1 == "Bacteria_Fungi_Farming_Old" && exists("bacteria_fungi_farming_old_network")) network1_obj <- bacteria_fungi_farming_old_network
        if (network1 == "Bacteria_Fungi_Farming_New" && exists("bacteria_fungi_farming_new_network")) network1_obj <- bacteria_fungi_farming_new_network

        if (network2 == "Bacteria_Fungi_Alpine_Old" && exists("bacteria_fungi_alpine_old_network")) network2_obj <- bacteria_fungi_alpine_old_network
        if (network2 == "Bacteria_Fungi_Alpine_New" && exists("bacteria_fungi_alpine_new_network")) network2_obj <- bacteria_fungi_alpine_new_network
        if (network2 == "Bacteria_Fungi_Farming_Old" && exists("bacteria_fungi_farming_old_network")) network2_obj <- bacteria_fungi_farming_old_network
        if (network2 == "Bacteria_Fungi_Farming_New" && exists("bacteria_fungi_farming_new_network")) network2_obj <- bacteria_fungi_farming_new_network

        if (!is.null(network1_obj) && !is.null(network2_obj)) {
          # Compare network properties
          cat("\nComparing", network1, "vs", network2, "\n")

          # 1. Compare degree distributions using Kolmogorov-Smirnov test
          tryCatch({
            ks_test <- ks.test(network1_obj$node_degrees, network2_obj$node_degrees)
            p_value <- ks_test$p.value
            significant <- ifelse(p_value < 0.05, "Yes", "No")

            test_results <- rbind(test_results, data.frame(
              Comparison = comparison_name,
              Property = "Degree Distribution",
              Test = "Kolmogorov-Smirnov",
              P_Value = p_value,
              Significant = significant,
              stringsAsFactors = FALSE
            ))

            cat("Degree Distribution (KS test): p-value =", p_value,
                ifelse(significant == "Yes", "(Significant)", "(Not Significant)"), "\n")
          }, error = function(e) {
            cat("Error in KS test for degree distribution:", e$message, "\n")
          })

          # 2. Compare betweenness distributions
          tryCatch({
            ks_test <- ks.test(network1_obj$betweenness, network2_obj$betweenness)
            p_value <- ks_test$p.value
            significant <- ifelse(p_value < 0.05, "Yes", "No")

            test_results <- rbind(test_results, data.frame(
              Comparison = comparison_name,
              Property = "Betweenness Distribution",
              Test = "Kolmogorov-Smirnov",
              P_Value = p_value,
              Significant = significant,
              stringsAsFactors = FALSE
            ))

            cat("Betweenness Distribution (KS test): p-value =", p_value,
                ifelse(significant == "Yes", "(Significant)", "(Not Significant)"), "\n")
          }, error = function(e) {
            cat("Error in KS test for betweenness distribution:", e$message, "\n")
          })

          # 3. Compare network density
          density1 <- edge_density(network1_obj$network)
          density2 <- edge_density(network2_obj$network)
          cat("Network Density:", network1, "=", density1, ",", network2, "=", density2, "\n")

          # 4. Compare modularity
          mod1 <- network1_obj$modularity
          mod2 <- network2_obj$modularity
          cat("Modularity:", network1, "=", mod1, ",", network2, "=", mod2, "\n")
        }
      }

      # Save test results to CSV
      write.csv(test_results, "cross_network_statistical_tests.csv", row.names = FALSE)
      cat("Statistical test results saved to cross_network_statistical_tests.csv\n")

      return(test_results)
    }

    # Run the tests
    cross_test_results <- perform_cross_network_tests()

    # Create visualization of test results
    if (nrow(cross_test_results) > 0) {
      # Create a heatmap of p-values
      cross_test_results$NegLog10P <- -log10(cross_test_results$P_Value)

      p5 <- ggplot(cross_test_results, aes(x = Comparison, y = Property, fill = NegLog10P)) +
        geom_tile() +
        scale_fill_gradient(low = "white", high = "red",
                           name = "-log10(p-value)",
                           guide = guide_colorbar(title.position = "top")) +
        geom_text(aes(label = ifelse(Significant == "Yes", "*", "")),
                  color = "black", size = 8) +
        theme_minimal() +
        theme(axis.text.x = element_text(angle = 45, hjust = 1)) +
        ggtitle("Statistical Significance of Cross-Network Property Differences")

      ggsave("cross_network_statistical_tests.pdf", p5, width = 8, height = 6)
      cat("Statistical test visualization saved to cross_network_statistical_tests.pdf\n")
    }
  } else {
    cat("Need at least 2 cross-networks to perform statistical tests\n")
  }
} else {
  cat("No cross-network data available for analysis\n")
}

cat("\n\nNetwork analysis completed. Check the PDF files for network visualizations.\n")
