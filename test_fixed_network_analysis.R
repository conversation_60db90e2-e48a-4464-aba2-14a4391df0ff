# Test the fixed network analysis function
library(readxl)
library(igraph)
library(vegan)
library(tidyverse)

# Configuration
TAXONOMIC_LEVEL <- "Family"
cat("Testing fixed network analysis at", TAXONOMIC_LEVEL, "level\n")

# Read one dataset for testing
bacteria_alpine_otu <- read_excel("OTU_table_bacteria_alpine_samples.xlsx")
bacteria_alpine_tax <- read_excel("taxonomy_bacteria_alpine_samples.xlsx")
bacteria_alpine_meta <- read_excel("metadata_bacteria_alpine_samples.xlsx")

# Function to aggregate OTUs to specified taxonomic level
aggregate_to_taxonomic_level <- function(otu_table, taxonomy_table, taxonomic_level) {
  cat("\n--- Aggregating to", taxonomic_level, "level ---\n")
  
  otu_id_col <- colnames(otu_table)[1]
  tax_otu_id_col <- colnames(taxonomy_table)[1]
  
  merged_data <- merge(otu_table, taxonomy_table[, c(tax_otu_id_col, taxonomic_level)], 
                       by.x = otu_id_col, by.y = tax_otu_id_col, all.x = TRUE)
  
  valid_rows <- !is.na(merged_data[[taxonomic_level]]) & 
                merged_data[[taxonomic_level]] != "" & 
                merged_data[[taxonomic_level]] != "NA"
  
  cat("Removed", sum(!valid_rows), "OTUs with missing", taxonomic_level, "assignment\n")
  merged_data <- merged_data[valid_rows, ]
  
  sample_cols <- setdiff(colnames(merged_data), c(otu_id_col, taxonomic_level))
  
  aggregated <- merged_data %>%
    group_by(!!sym(taxonomic_level)) %>%
    summarise(across(all_of(sample_cols), \(x) sum(x, na.rm = TRUE)), .groups = 'drop')
  
  colnames(aggregated)[1] <- otu_id_col
  cat("Aggregated from", nrow(otu_table), "OTUs to", nrow(aggregated), taxonomic_level, "groups\n")
  
  return(as.data.frame(aggregated))
}

# Function to filter samples by environment
filter_samples_by_environment <- function(otu_table, metadata, environment_type) {
  sample_id_col <- if ("Sample_ID" %in% colnames(metadata)) "Sample_ID" else "sample"
  target_samples <- metadata[[sample_id_col]][grepl(environment_type, metadata$Environment, ignore.case = TRUE)]
  cat("Found", length(target_samples), "target samples for environment", environment_type, "\n")
  
  sample_cols <- which(colnames(otu_table) %in% target_samples)
  if (length(sample_cols) == 0) return(NULL)
  
  filtered_table <- otu_table[, c(1, sample_cols)]
  cat("Filtered", environment_type, "samples:", ncol(filtered_table) - 1, "samples\n")
  return(filtered_table)
}

# Function to create correlation matrix for taxonomic-level data
create_taxonomic_correlation_matrix <- function(taxonomic_table, threshold = 0.6) {
  cat("\n--- Creating correlation matrix ---\n")
  cat("Input dimensions:", dim(taxonomic_table), "\n")

  tryCatch({
    abundance_matrix <- as.matrix(taxonomic_table[,-1])
    rownames(abundance_matrix) <- taxonomic_table[[1]]

    row_sums <- rowSums(abundance_matrix)
    constant_rows <- row_sums == 0 | apply(abundance_matrix, 1, function(x) length(unique(x)) <= 1)
    
    if (any(constant_rows)) {
      cat("Removing", sum(constant_rows), "constant rows\n")
      abundance_matrix <- abundance_matrix[!constant_rows, , drop = FALSE]
    }

    cat("Final matrix for correlation:", nrow(abundance_matrix), "taxa ×", ncol(abundance_matrix), "samples\n")

    cor_matrix <- cor(t(abundance_matrix), method = "spearman", use = "pairwise.complete.obs")

    if (any(is.na(cor_matrix))) {
      cor_matrix[is.na(cor_matrix)] <- 0
    }

    cor_matrix[abs(cor_matrix) < threshold] <- 0
    diag(cor_matrix) <- 0

    cat("After thresholding: ", sum(cor_matrix != 0), " non-zero correlations\n")

    return(list(
      cor_matrix = cor_matrix,
      taxa_names = rownames(abundance_matrix),
      n_taxa = nrow(abundance_matrix)
    ))
    
  }, error = function(e) {
    cat("Error in correlation calculation:", e$message, "\n")
    return(NULL)
  })
}

# Fixed function to analyze taxonomic network
analyze_taxonomic_network <- function(cor_result, group_name) {
  if (is.null(cor_result)) {
    cat("Cannot analyze network: correlation result is NULL\n")
    return(NULL)
  }
  
  cat("\n========== ANALYZING", group_name, "NETWORK ==========\n")
  
  cor_matrix <- cor_result$cor_matrix
  taxa_names <- cor_result$taxa_names
  
  if (sum(cor_matrix != 0) == 0) {
    cat("No correlations above threshold. Cannot create network.\n")
    return(NULL)
  }
  
  tryCatch({
    # Create igraph network from correlation matrix
    network <- graph_from_adjacency_matrix(
      abs(cor_matrix), 
      mode = "undirected", 
      weighted = TRUE, 
      diag = FALSE
    )
    
    # Add taxa names as vertex names
    V(network)$name <- taxa_names
    
    # Get edge list to properly assign edge attributes
    edge_list <- as_edgelist(network, names = FALSE)
    
    # Extract correlation values for each edge
    edge_correlations <- numeric(nrow(edge_list))
    edge_signs <- character(nrow(edge_list))
    
    for (i in 1:nrow(edge_list)) {
      row_idx <- edge_list[i, 1]
      col_idx <- edge_list[i, 2]
      correlation_value <- cor_matrix[row_idx, col_idx]
      edge_correlations[i] <- correlation_value
      edge_signs[i] <- ifelse(correlation_value > 0, "positive", "negative")
    }
    
    # Add edge attributes
    E(network)$sign <- edge_signs
    E(network)$correlation <- edge_correlations
    
    # Calculate network metrics
    cat("Network created with", vcount(network), "nodes and", ecount(network), "edges\n")
    
    node_degrees <- degree(network)
    cat("Average degree:", mean(node_degrees), "\n")
    cat("Max degree:", max(node_degrees), "\n")
    
    betweenness <- betweenness(network)
    closeness <- closeness(network)
    
    communities <- cluster_louvain(network)
    community_count <- length(communities)
    modularity_score <- modularity(communities)
    
    cat("Communities detected:", community_count, "\n")
    cat("Modularity:", modularity_score, "\n")
    
    degree_threshold <- quantile(node_degrees, 0.9)
    hub_nodes <- names(node_degrees[node_degrees >= degree_threshold])
    cat("Hub nodes (top 10% by degree):", length(hub_nodes), "\n")
    
    results <- list(
      network = network,
      node_degrees = node_degrees,
      betweenness = betweenness,
      closeness = closeness,
      communities = communities,
      community_count = community_count,
      modularity = modularity_score,
      hub_nodes = hub_nodes,
      taxa_names = taxa_names,
      group_name = group_name
    )
    
    return(results)
    
  }, error = function(e) {
    cat("Error in network analysis:", e$message, "\n")
    return(NULL)
  })
}

# Test the complete workflow
cat("\n========== TESTING COMPLETE WORKFLOW ==========\n")

# 1. Aggregate to family level
bacteria_alpine_agg <- aggregate_to_taxonomic_level(bacteria_alpine_otu, bacteria_alpine_tax, TAXONOMIC_LEVEL)

# 2. Filter to old samples
bacteria_alpine_old <- filter_samples_by_environment(bacteria_alpine_agg, bacteria_alpine_meta, "old")

# 3. Create correlation matrix
if (!is.null(bacteria_alpine_old)) {
  bacteria_alpine_old_cor <- create_taxonomic_correlation_matrix(bacteria_alpine_old, threshold = 0.6)
  
  # 4. Analyze network
  if (!is.null(bacteria_alpine_old_cor)) {
    bacteria_alpine_old_network <- analyze_taxonomic_network(bacteria_alpine_old_cor, "Bacteria_Alpine_Old")
    
    if (!is.null(bacteria_alpine_old_network)) {
      cat("\n🎉 COMPLETE SUCCESS! 🎉\n")
      cat("Network analysis completed successfully!\n")
      cat("Hub families:", paste(head(bacteria_alpine_old_network$hub_nodes, 5), collapse = ", "), "\n")
    } else {
      cat("FAILED: Network analysis failed\n")
    }
  } else {
    cat("FAILED: Could not create correlation matrix\n")
  }
} else {
  cat("FAILED: Could not filter samples\n")
}

cat("\nFixed network analysis test completed!\n")
